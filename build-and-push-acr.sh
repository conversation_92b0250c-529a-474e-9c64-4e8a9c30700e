#!/bin/bash

# Build and Push Docker Image to Azure Container Registry
# Usage: ./build-and-push-acr.sh <ACR_NAME> <ACR_USERNAME> <ACR_PASSWORD> [TAG] [ENVIRONMENT]

set -e

# Function to display usage
usage() {
    echo "Usage: $0 <ACR_NAME> <ACR_USERNAME> <ACR_PASSWORD> [TAG] [ENVIRONMENT]"
    echo ""
    echo "Parameters:"
    echo "  ACR_NAME      - Azure Container Registry name (required)"
    echo "  ACR_USERNAME  - ACR username (required)"
    echo "  ACR_PASSWORD  - ACR password (required)"
    echo "  TAG           - Docker image tag (optional, default: latest-staging)"
    echo "  ENVIRONMENT   - ASP.NET Core environment (optional, default: Staging)"
    echo ""
    echo "Example:"
    echo "  $0 myacr myuser mypassword latest-staging Staging"
    exit 1
}

# Check if required parameters are provided
if [ $# -lt 3 ]; then
    echo "Error: Missing required parameters"
    usage
fi

# Parse parameters
ACR_NAME="$1"
ACR_USERNAME="$2"
ACR_PASSWORD="$3"
TAG="${4:-latest-staging}"
ENVIRONMENT="${5:-Staging}"

# Validate parameters
if [ -z "$ACR_NAME" ] || [ -z "$ACR_USERNAME" ] || [ -z "$ACR_PASSWORD" ]; then
    echo "Error: ACR_NAME, ACR_USERNAME, and ACR_PASSWORD cannot be empty"
    usage
fi

# Set variables
ACR_REGISTRY="${ACR_NAME}.azurecr.io"
IMAGE_NAME="aibase"
FULL_IMAGE_TAG="${ACR_REGISTRY}/${IMAGE_NAME}:${TAG}"
BUILD_CONTEXT="./src"

echo "=========================================="
echo "Building and pushing Docker image to ACR"
echo "=========================================="
echo "Registry: ${ACR_REGISTRY}"
echo "Image: ${IMAGE_NAME}"
echo "Tag: ${TAG}"
echo "Full image tag: ${FULL_IMAGE_TAG}"
echo "Build context: ${BUILD_CONTEXT}"
echo "Environment: ${ENVIRONMENT}"
echo "Platform: linux/amd64"
echo "=========================================="

# Check if build context exists
if [ ! -d "$BUILD_CONTEXT" ]; then
    echo "Error: Build context directory '$BUILD_CONTEXT' does not exist"
    exit 1
fi

# Check if Dockerfile exists
if [ ! -f "$BUILD_CONTEXT/Dockerfile" ]; then
    echo "Error: Dockerfile not found in '$BUILD_CONTEXT'"
    exit 1
fi

# Set up Docker Buildx for multi-platform builds
echo "Setting up Docker Buildx..."
docker buildx create --use --name acr-builder --driver docker-container 2>/dev/null || docker buildx use acr-builder

# Log in to ACR
echo "Logging in to Azure Container Registry..."
echo "$ACR_PASSWORD" | docker login "$ACR_REGISTRY" -u "$ACR_USERNAME" --password-stdin

if [ $? -ne 0 ]; then
    echo "Error: Failed to log in to ACR"
    exit 1
fi

echo "Successfully logged in to $ACR_REGISTRY"

# Build and push Docker image
echo "Building and pushing Docker image..."
docker buildx build \
    --platform linux/amd64 \
    --context "$BUILD_CONTEXT" \
    --push \
    --tag "$FULL_IMAGE_TAG" \
    --build-arg ASPNETCORE_ENVIRONMENT="$ENVIRONMENT" \
    "$BUILD_CONTEXT"

if [ $? -ne 0 ]; then
    echo "Error: Failed to build and push Docker image"
    docker logout "$ACR_REGISTRY"
    exit 1
fi

echo "Successfully built and pushed image: $FULL_IMAGE_TAG"

# Log out from ACR
echo "Logging out from Azure Container Registry..."
docker logout "$ACR_REGISTRY"

echo "=========================================="
echo "Build and push completed successfully!"
echo "Image: $FULL_IMAGE_TAG"
echo "=========================================="
