namespace aibase.DataEntries.Dto;

/// <summary>
/// 
/// </summary>
public class ImageCropDetailDto
{
    /// <summary>
    /// 
    /// </summary>
    public int RockLineId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int ImageCropId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double StartDepth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double StartX { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double EndDepth { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public double EndX { get; set; }
}