using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.AssaySuites;
using aibase.AssaySuites.Dto;
using aibase.DataEntries.Dto;
using aibase.DataEntries.Services;
using aibase.Downholes.Dto;
using aibase.Downholes.Services.AssayService;
using aibase.Downholes.Services.DownholeSurveyService;
using aibase.Downholes.Services.GeophysicsService;
using aibase.DrillHoleEntity;
using aibase.GeologySuites;
using aibase.GeologySuites.Dto;
using aibase.ImportDataServices.Dto;
using aibase.ImportMappingTemplates;
using aibase.Suites;
using aibase.Suites.Dto;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using OfficeOpenXml;

namespace aibase.ImportDataServices.Services;

/// <inheritdoc />
public class ImportDataService : IImportDataService
{
    private readonly IRepository<ImportMappingTemplate, int> _importMappingTemplateRepository;
    private readonly IRepository<ImportMappingTemplateField, int> _importMappingTemplateFieldRepository;
    private readonly IRepository<Suite, int> _suiteRepository;
    private readonly IRepository<AssaySuite, int> _assaySuiteRepository;
    private readonly IRepository<DrillHole, int> _drillHoleRepository;
    private readonly IRepository<GeologySuite, int> _geologySuiteRepository;
    private readonly IGeophysicsService _geophysicsService;
    private readonly IDataEntryService _dataEntryService;
    private readonly IAssayService _assayService;
    private readonly IDownholeSurveyService _downholeSurveyService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public ImportDataService(
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper,
        IRepository<ImportMappingTemplate, int> importMappingTemplateRepository,
        IRepository<ImportMappingTemplateField, int> importMappingTemplateFieldRepository,
        IRepository<Suite, int> suiteRepository,
        IRepository<AssaySuite, int> assaySuiteRepository,
        IRepository<DrillHole, int> drillHoleRepository,
        IGeophysicsService geophysicsService,
        IAssayService assayService,
        IDownholeSurveyService downholeSurveyService,
        IRepository<GeologySuite, int> geologySuiteRepository,
        IDataEntryService dataEntryService)
    {
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
        _importMappingTemplateRepository = importMappingTemplateRepository;
        _importMappingTemplateFieldRepository = importMappingTemplateFieldRepository;
        _suiteRepository = suiteRepository;
        _assaySuiteRepository = assaySuiteRepository;
        _drillHoleRepository = drillHoleRepository;
        _geophysicsService = geophysicsService;
        _assayService = assayService;
        _downholeSurveyService = downholeSurveyService;
        _geologySuiteRepository = geologySuiteRepository;
        _dataEntryService = dataEntryService;
    }

    /// <inheritdoc />
    public async Task<ImportMappingTemplateDto> SaveTemplateAsync(SaveTemplateDto input)
    {
        return input.Id.HasValue
            ? await UpdateTemplateAsync(input)
            : await CreateTemplateAsync(input);
    }

    /// <summary>
    /// Creates a new import mapping template
    /// </summary>
    public async Task<ImportMappingTemplateDto> CreateTemplateAsync(SaveTemplateDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var template = new ImportMappingTemplate
        {
            Name = input.Name,
            ImportFileType = input.ImportFileType,
            SuiteId = input.SuiteId,
            TenantId = tenantId
        };

        await _importMappingTemplateRepository.InsertAsync(template);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        var fields = input.Fields.Select(item => new ImportMappingTemplateField
        {
            TenantId = tenantId,
            SystemFieldName = item.SystemFieldName,
            FileColumnName = item.FileColumnName,
            ImportMappingTemplateId = template.Id,
            Sequence = item.Sequence
        }).ToList();

        foreach (var item in fields)
        {
            await _importMappingTemplateFieldRepository.InsertAsync(item);
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();

        // Get the saved template with its fields for return
        var savedTemplate = await _importMappingTemplateRepository
            .GetAllIncluding(x => x.ImportMappingTemplateFields)
            .FirstOrDefaultAsync(t => t.Name == input.Name && t.TenantId == tenantId);

        if (savedTemplate == null)
        {
            throw new UserFriendlyException("Failed to retrieve the saved template.");
        }

        var result = _mapper.Map<ImportMappingTemplateDto>(savedTemplate);
        result.Id = savedTemplate.Id;

        return result;
    }

    /// <summary>
    /// Updates an existing import mapping template
    /// </summary>
    public async Task<ImportMappingTemplateDto> UpdateTemplateAsync(SaveTemplateDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        if (!input.Id.HasValue)
        {
            throw new UserFriendlyException("Id is required.");
        }

        var existingTemplate = await _importMappingTemplateRepository.GetAsync(input.Id.Value);
        if (existingTemplate == null)
        {
            throw new UserFriendlyException("Template not found.");
        }

        if (existingTemplate.TenantId != tenantId)
        {
            throw new UserFriendlyException("You don't have permission to update this template.");
        }

        existingTemplate.Name = input.Name;
        existingTemplate.ImportFileType = input.ImportFileType;
        existingTemplate.SuiteId = input.SuiteId;

        await _importMappingTemplateRepository.UpdateAsync(existingTemplate);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        // Delete existing fields
        var existingFields =
            await _importMappingTemplateFieldRepository.GetAllListAsync(f =>
                f.ImportMappingTemplateId == existingTemplate.Id);
        foreach (var field in existingFields)
        {
            await _importMappingTemplateFieldRepository.DeleteAsync(field);
        }

        // Insert new fields
        var fields = input.Fields.Select(item => new ImportMappingTemplateField()
        {
            TenantId = tenantId,
            SystemFieldName = item.SystemFieldName,
            FileColumnName = item.FileColumnName,
            ImportMappingTemplateId = existingTemplate.Id,
            Sequence = item.Sequence
        }).ToList();

        foreach (var item in fields)
        {
            await _importMappingTemplateFieldRepository.InsertAsync(item);
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();

        // Get the updated template with its fields
        var updatedTemplate = await _importMappingTemplateRepository
            .GetAllIncluding(x => x.ImportMappingTemplateFields)
            .FirstOrDefaultAsync(t => t.Id == input.Id.Value);

        if (updatedTemplate == null)
        {
            throw new UserFriendlyException("Failed to retrieve the updated template.");
        }

        var result = _mapper.Map<ImportMappingTemplateDto>(updatedTemplate);
        result.Id = updatedTemplate.Id;

        return result;
    }

    /// <inheritdoc />
    public async Task<ImportMappingTemplateDto> GetImportTemplateAsync(EntityDto<int> input)
    {
        var importMappingTemplate = await _importMappingTemplateRepository
            .GetAllIncluding(x => x.ImportMappingTemplateFields)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.Id);
        if (importMappingTemplate == null)
        {
            throw new EntityNotFoundException(typeof(ImportMappingTemplate), input.Id);
        }

        var importMappingTemplateDto = _mapper.Map<ImportMappingTemplateDto>(importMappingTemplate);
        importMappingTemplateDto.ImportMappingTemplateFields = importMappingTemplateDto.ImportMappingTemplateFields
            .OrderBy(x => x.Sequence).ToList();

        // Get suite name based on import file type
        if (importMappingTemplate.SuiteId.HasValue)
        {
            switch (importMappingTemplate.ImportFileType)
            {
                case ImportFileType.Geology:
                    var geologySuite = await _geologySuiteRepository.GetAll()
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Id == importMappingTemplate.SuiteId);
                    importMappingTemplateDto.SuiteName = geologySuite?.Name ?? string.Empty;
                    break;

                case ImportFileType.Geophysics:
                    var geophysicsSuite = await _suiteRepository.GetAll()
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Id == importMappingTemplate.SuiteId);
                    importMappingTemplateDto.SuiteName = geophysicsSuite?.Name ?? string.Empty;
                    break;

                case ImportFileType.Assay:
                    var assaySuite = await _assaySuiteRepository.GetAll()
                        .AsNoTracking()
                        .FirstOrDefaultAsync(x => x.Id == importMappingTemplate.SuiteId);
                    importMappingTemplateDto.SuiteName = assaySuite?.Name ?? string.Empty;
                    break;
            }
        }

        return importMappingTemplateDto;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ImportMappingTemplateDto>> GetAllAsync(
        PagedImportMappingTemplateResultRequestDto input)
    {
        var query = _importMappingTemplateRepository.GetAllIncluding(x => x.ImportMappingTemplateFields)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var templates = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var templateDtos = _mapper.Map<List<ImportMappingTemplateDto>>(templates);

        // Group templates by ImportFileType for efficient suite lookup
        var geologySuiteIds = templates.Where(t => t.ImportFileType == ImportFileType.Geology && t.SuiteId.HasValue)
            .Select(t => t.SuiteId.Value).Distinct().ToList();
        var geophysicsSuiteIds = templates
            .Where(t => t.ImportFileType == ImportFileType.Geophysics && t.SuiteId.HasValue)
            .Select(t => t.SuiteId.Value).Distinct().ToList();
        var assaySuiteIds = templates.Where(t => t.ImportFileType == ImportFileType.Assay && t.SuiteId.HasValue)
            .Select(t => t.SuiteId.Value).Distinct().ToList();

        // Fetch all required suites in parallel
        var geologySuites = await _geologySuiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => geologySuiteIds.Contains(x.Id))
            .ToDictionaryAsync(x => x.Id, x => x.Name);

        var geophysicsSuites = await _suiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => geophysicsSuiteIds.Contains(x.Id))
            .ToDictionaryAsync(x => x.Id, x => x.Name);

        var assaySuites = await _assaySuiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => assaySuiteIds.Contains(x.Id))
            .ToDictionaryAsync(x => x.Id, x => x.Name);

        // Populate SuiteName for each template
        foreach (var templateDto in templateDtos)
        {
            var template = templates.First(t => t.Id == templateDto.Id);
            if (!template.SuiteId.HasValue) continue;

            switch (template.ImportFileType)
            {
                case ImportFileType.Geology:
                    templateDto.SuiteName = geologySuites.GetValueOrDefault(template.SuiteId.Value) ?? string.Empty;
                    break;
                case ImportFileType.Geophysics:
                    templateDto.SuiteName = geophysicsSuites.GetValueOrDefault(template.SuiteId.Value) ?? string.Empty;
                    break;
                case ImportFileType.Assay:
                    templateDto.SuiteName = assaySuites.GetValueOrDefault(template.SuiteId.Value) ?? string.Empty;
                    break;
            }
        }

        return new PagedResultDto<ImportMappingTemplateDto>(totalCount, templateDtos);
    }

    /// <inheritdoc />
    public async Task<IActionResult> PreviewMappingFieldsAsync(PreviewMappingFieldsDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();
        var matchingTypes = new object();

        switch (input.ImportFileType)
        {
            case ImportFileType.Geology:
            {
                if (!input.SuiteId.HasValue)
                {
                    return new OkObjectResult(new { type = "Unknown" });
                }

                var geologySuite = await _geologySuiteRepository.GetAll()
                    .AsNoTracking()
                    .Include(x => x.GeologySuiteFields)
                    .FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Id == input.SuiteId);

                if (geologySuite != null)
                {
                    matchingTypes = new
                    {
                        type = "GeologySuite",
                        geologySuiteId = _mapper.Map<GeologySuiteDto>(geologySuite),
                        matchedAttributes = new List<string> { "DrillHole", "Depth From", "Depth To" }
                            .Concat(geologySuite.GeologySuiteFields.OrderBy(x => x.Sequence)
                                .Select(x => x.Name))
                            .ToList()
                    };
                }

                break;
            }
            case ImportFileType.Geophysics:
            {
                if (!input.SuiteId.HasValue)
                {
                    return new OkObjectResult(new { type = "Unknown" });
                }

                var suite = await _suiteRepository.GetAll()
                    .AsNoTracking()
                    .Include(x => x.SuiteAttributes)
                    .ThenInclude(x => x.Attribute)
                    .FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Id == input.SuiteId);

                if (suite != null)
                {
                    matchingTypes = new
                    {
                        type = "GeophysicsSuite",
                        suiteId = _mapper.Map<SuiteDto>(suite),
                        matchedAttributes = new List<string> { "DrillHole", "Depth (m)" }
                            .Concat(suite.SuiteAttributes
                                .Select(x => x.Attribute.Name))
                            .ToList()
                    };
                }

                break;
            }
            case ImportFileType.Assay:
            {
                if (!input.SuiteId.HasValue)
                {
                    return new OkObjectResult(new { type = "Unknown" });
                }

                var assaySuite = await _assaySuiteRepository
                    .GetAll()
                    .AsNoTracking()
                    .Include(x => x.AssaySuiteAttributes)
                    .ThenInclude(x => x.AssayAttribute)
                    .FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Id == input.SuiteId);

                if (assaySuite != null)
                {
                    matchingTypes = new
                    {
                        type = "AssaySuite",
                        assaySuiteId = _mapper.Map<AssaySuiteDto>(assaySuite),
                        matchedAttributes = new List<string> { "DrillHole", "Depth From", "Depth To" }
                            .Concat(assaySuite.AssaySuiteAttributes
                                .Select(x => x.AssayAttribute.Name))
                            .ToList()
                    };
                }

                break;
            }
            case ImportFileType.DownholeSurvey:
            {
                var downholeSurveyHeaders = new[] { "Drill Hole", "Depth", "Dip", "Azimuth", "Type" };
                matchingTypes = new
                {
                    type = "DownholeSurvey",
                    matchedHeaders = downholeSurveyHeaders
                };
                break;
            }

            case ImportFileType.DrillHole:
            {
                var drillHoleHeaders = new[]
                {
                    "Name", "Max Depth", "Elevation", "Northing", "Easting", "Longitude", "Latitude", "Dip", "Azimuth"
                };
                matchingTypes = new
                {
                    type = "DrillHole",
                    matchedHeaders = drillHoleHeaders
                };
                break;
            }
            default:
                return new OkObjectResult(new { type = "Unknown" });
        }

        return new OkObjectResult(matchingTypes);
    }

    /// <inheritdoc />
    public async Task<IActionResult> ImportFileAsync([FromForm] UploadDataDto input)
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        var importType = input.ImportFileType;
        var suiteId = input.SuiteId;
        var countRecord = 0;
        var errors = new List<string>();

        var importMappingTemplate = await _importMappingTemplateRepository
            .GetAllIncluding(x => x.ImportMappingTemplateFields)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.ImportMappingTemplateId);
        if (importMappingTemplate != null)
        {
            importType = importMappingTemplate.ImportFileType;
            suiteId = importMappingTemplate.SuiteId;
        }

        var rawImportMappingFields = input.ImportMappingFields;
        var importMappingFields =
            JsonConvert.DeserializeObject<List<ImportMappingFieldDto>>(rawImportMappingFields ?? "[]") ?? [];

        var mappingList = importMappingTemplate?.ImportMappingTemplateFields.ToList() ?? importMappingFields
            .Select(x => new ImportMappingTemplateField
            {
                SystemFieldName = x.SystemFieldName,
                FileColumnName = x.FileColumnName
            }).ToList();

        if (mappingList.Count == 0)
        {
            return new BadRequestObjectResult("Mapping template list is required.");
        }

        var createdDrillHoles = new List<DrillHole>();
        var existingDrillHoleNames = new HashSet<string>(await _drillHoleRepository
            .GetAll()
            .AsNoTracking()
            .Where(d => d.TenantId == _abpSession.GetTenantId() &&
                        d.ProjectId == input.ProjectId &&
                        d.ProspectId == input.ProspectId)
            .Select(d => d.Name)
            .ToListAsync());

        var drillHoleNamesFromFile = new HashSet<string>();
        using (var stream = input.ExcelFile.OpenReadStream())
        using (var package = new ExcelPackage(stream))
        {
            var worksheet = package.Workbook.Worksheets[0];
            if (worksheet.Dimension?.Rows > 1)
            {
                for (var row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    var drillHoleName = worksheet.Cells[row, 1].Text?.Trim();
                    if (!string.IsNullOrEmpty(drillHoleName))
                    {
                        drillHoleNamesFromFile.Add(drillHoleName);
                    }
                }
            }
        }

        var drillHoleNames = drillHoleNamesFromFile.Distinct().ToList();

        if (input.CreateNewDrillHoles != null && input.ImportFileType != ImportFileType.DrillHole)
        {
            if (input.CreateNewDrillHoles == true)
            {
                foreach (var drillHoleName in drillHoleNames)
                {
                    if (!existingDrillHoleNames.Contains(drillHoleName))
                    {
                        var newDrillHole = new DrillHole
                        {
                            Name = drillHoleName,
                            TenantId = _abpSession.GetTenantId(),
                            ProjectId = input.ProjectId,
                            ProspectId = input.ProspectId,
                            IsActive = true,
                            DrillHoleStatus = DrillHoleStatus.NotStarted,
                            MaxDepth = 0
                        };

                        var insertedDrillHole = await _drillHoleRepository.InsertAsync(newDrillHole);
                        existingDrillHoleNames.Add(drillHoleName);
                        createdDrillHoles.Add(insertedDrillHole);
                    }
                }

                await _unitOfWorkManager.Current.SaveChangesAsync();
            }
            else
            {
                var missingDrillHoles = drillHoleNames
                    .Where(name => existingDrillHoleNames.Contains(name))
                    .ToList();

                if (missingDrillHoles.Count == 0)
                {
                    var missingDrillHolesList = string.Join(", ", missingDrillHoles);
                    return new BadRequestObjectResult(
                        $"Drillhole(s) {missingDrillHolesList} have not been created yet.");
                }
            }
        }

        var drillHoles = await _drillHoleRepository.GetAll()
            .Where(x => x.TenantId == _abpSession.GetTenantId())
            .ToListAsync();

        if (drillHoles.Count == 0 && input.ImportFileType != ImportFileType.DrillHole)
        {
            return new BadRequestObjectResult("There are no drill holes in the Project.");
        }

        switch (importType)
        {
            case ImportFileType.Geology:
            {
                if (suiteId == null)
                {
                    return new BadRequestObjectResult("SuiteId is required");
                }

                var uploadDownholeDataDto = new UploadDataEntryByImportTemplateDto
                {
                    ProjectId = input.ProjectId,
                    ProspectId = input.ProspectId,
                    SuiteId = (int)suiteId,
                    ImportMappingTemplateFields = mappingList,
                    ExcelFile = input.ExcelFile,
                    DrillHoles = drillHoles,
                    ImportMode = input.ImportMode
                };

                errors = await _dataEntryService.ValidationDataEntryDataAsync(uploadDownholeDataDto);
                if (errors.Count > 0)
                {
                    return new BadRequestObjectResult(errors);
                }

                switch (input.ImportMode)
                {
                    case ImportMode.Add:
                    {
                        var uploadResult =
                            await _dataEntryService.UploadDataEntryByImportTemplateAsync(uploadDownholeDataDto);
                        countRecord = uploadResult.Count;
                        errors.AddRange(uploadResult.Errors);
                        break;
                    }
                    case ImportMode.Update:
                    {
                        var uploadResult =
                            await _dataEntryService.UpdateDataEntryByImportTemplateAsync(uploadDownholeDataDto);
                        countRecord = uploadResult.Count;
                        errors.AddRange(uploadResult.Errors);
                        break;
                    }
                    case ImportMode.AddAndUpdate:
                    {
                        var uploadResult =
                            await _dataEntryService.AddAndUpdateDataEntryByImportTemplateAsync(uploadDownholeDataDto);
                        countRecord = uploadResult.Count;
                        errors.AddRange(uploadResult.Errors);
                        break;
                    }
                    default:
                        throw new ArgumentOutOfRangeException();
                }

                break;
            }
            case ImportFileType.Geophysics:
            {
                if (suiteId == null)
                {
                    return new BadRequestObjectResult("SuiteId is required");
                }

                var uploadDownholeDataDto = new UploadGeophysicsDataBySuiteDto
                {
                    ProjectId = input.ProjectId,
                    ProspectId = input.ProspectId,
                    SuiteId = (int)suiteId,
                    ImportMappingTemplateFields = mappingList,
                    ExcelFile = input.ExcelFile,
                    DrillHoles = drillHoles,
                    ImportMode = input.ImportMode
                };

                errors = await _geophysicsService.ValidationGeophysicsDataAsync(uploadDownholeDataDto);
                if (errors.Count > 0)
                {
                    return new BadRequestObjectResult(errors);
                }

                switch (input.ImportMode)
                {
                    case ImportMode.Add:
                    {
                        countRecord =
                            await _geophysicsService.UploadGeophysicsDataByImportTemplateAsync(uploadDownholeDataDto);
                        break;
                    }
                    case ImportMode.Update:
                    {
                        countRecord =
                            await _geophysicsService.UpdateGeophysicsDataByImportTemplateAsync(uploadDownholeDataDto);
                        break;
                    }
                    case ImportMode.AddAndUpdate:
                    {
                        countRecord =
                            await _geophysicsService.AddAndUpdateGeophysicsDataByImportTemplateAsync(
                                uploadDownholeDataDto);
                        break;
                    }
                    default:
                        throw new ArgumentOutOfRangeException();
                }

                break;
            }
            case ImportFileType.Assay:
            {
                if (suiteId == null)
                {
                    return new BadRequestObjectResult("SuiteId is required");
                }

                var uploadAssayDataByAssaySuiteDto = new UploadAssayDataByAssaySuiteDto
                {
                    ProjectId = input.ProjectId,
                    ProspectId = input.ProspectId,
                    AssaySuiteId = (int)suiteId,
                    ImportMappingTemplateFields = mappingList,
                    ExcelFile = input.ExcelFile,
                    DrillHoles = drillHoles,
                    ImportMode = input.ImportMode
                };

                errors = await _assayService.ValidationAssayDataAsync(uploadAssayDataByAssaySuiteDto);
                if (errors.Count > 0)
                {
                    return new BadRequestObjectResult(errors);
                }

                switch (input.ImportMode)
                {
                    case ImportMode.Add:
                    {
                        countRecord =
                            await _assayService.UploadAssayDataByImportTemplateAsync(uploadAssayDataByAssaySuiteDto);
                        break;
                    }
                    case ImportMode.Update:
                        countRecord =
                            await _assayService.UpdateAssayDataByImportTemplateAsync(uploadAssayDataByAssaySuiteDto);
                        break;
                    case ImportMode.AddAndUpdate:
                    {
                        countRecord =
                            await _assayService.AddAndUpdateAssayDataByImportTemplateAsync(
                                uploadAssayDataByAssaySuiteDto);
                        break;
                    }
                    default:
                        throw new ArgumentOutOfRangeException();
                }

                break;
            }
            case ImportFileType.DownholeSurvey:
            {
                var uploadDownholeSurveyDataDto = new UploadDownholeSurveyDataDto
                {
                    ProjectId = input.ProjectId,
                    ProspectId = input.ProspectId,
                    ExcelFile = input.ExcelFile,
                    ImportMappingTemplateFields = mappingList,
                    DrillHoles = drillHoles,
                    ImportMode = input.ImportMode
                };

                errors = await _downholeSurveyService.ValidationDownholeSurveyDataAsync(uploadDownholeSurveyDataDto);
                if (errors.Count > 0)
                {
                    return new BadRequestObjectResult(errors);
                }

                switch (input.ImportMode)
                {
                    case ImportMode.Add:
                    {
                        countRecord =
                            await _downholeSurveyService.UploadDownholeSurveyDataAsync(uploadDownholeSurveyDataDto,
                                false);
                        break;
                    }
                    case ImportMode.Update:
                    {
                        countRecord =
                            await _downholeSurveyService.UpdateDownholeSurveyDataAsync(uploadDownholeSurveyDataDto);
                        break;
                    }
                    case ImportMode.AddAndUpdate:
                    {
                        countRecord =
                            await _downholeSurveyService.AddAndUpdateDownholeSurveyDataAsync(
                                uploadDownholeSurveyDataDto);
                        break;
                    }
                    default:
                        throw new ArgumentOutOfRangeException();
                }

                break;
            }
            case ImportFileType.DrillHole:
            {
                // Process Excel file data
                using (var stream = input.ExcelFile.OpenReadStream())
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];
                    if (worksheet.Dimension?.Rows > 1)
                    {
                        var columnMappings = mappingList.ToDictionary(
                            x => x.SystemFieldName,
                            x => worksheet.Cells[1, 1, 1, worksheet.Dimension.Columns]
                                .First(c => c.Text.Trim() == x.FileColumnName).Start.Column);

                        for (var row = 2; row <= worksheet.Dimension.Rows; row++)
                        {
                            var name = GetCellValue<string>(worksheet, row, columnMappings, "Name");
                            if (string.IsNullOrEmpty(name))
                            {
                                errors.Add($"Row {row}: Name is required");
                                continue;
                            }

                            var existingDrillHole = drillHoles.FirstOrDefault(d => d.Name == name);
                            var drillHole = existingDrillHole ?? new DrillHole
                            {
                                Name = name,
                                TenantId = _abpSession.GetTenantId(),
                                IsActive = true,
                                DrillHoleStatus = DrillHoleStatus.NotStarted
                            };

                            var drillHoleExist =
                                await _drillHoleRepository.FirstOrDefaultAsync(d =>
                                    d.TenantId == _abpSession.GetTenantId() && d.Name == drillHole.Name);

                            drillHole.Elevation = GetCellValue<decimal?>(worksheet, row, columnMappings, "Elevation");
                            drillHole.Northing = GetCellValue<decimal?>(worksheet, row, columnMappings, "Northing");
                            drillHole.Easting = GetCellValue<decimal?>(worksheet, row, columnMappings, "Easting");
                            drillHole.Longitude = GetCellValue<decimal?>(worksheet, row, columnMappings, "Longitude");
                            drillHole.Latitude = GetCellValue<decimal?>(worksheet, row, columnMappings, "Latitude");
                            drillHole.Dip = GetCellValue<decimal?>(worksheet, row, columnMappings, "Dip");
                            drillHole.Azimuth = GetCellValue<decimal?>(worksheet, row, columnMappings, "Azimuth");
                            drillHole.MaxDepth = GetCellValue<double>(worksheet, row, columnMappings, "Max Depth");
                            drillHole.ProjectId = input.ProjectId;
                            drillHole.ProspectId = input.ProspectId;

                            switch (input.ImportMode)
                            {
                                case ImportMode.Add when existingDrillHole == null:
                                {
                                    if (drillHoleExist == null)
                                    {
                                        await _drillHoleRepository.InsertAsync(drillHole);
                                        createdDrillHoles.Add(drillHole);
                                    }

                                    break;
                                }
                                case ImportMode.Update when existingDrillHole != null:
                                    await _drillHoleRepository.UpdateAsync(drillHole);
                                    break;
                                case ImportMode.AddAndUpdate:
                                    if (existingDrillHole == null)
                                    {
                                        if (drillHoleExist == null)
                                        {
                                            await _drillHoleRepository.InsertAsync(drillHole);
                                            createdDrillHoles.Add(drillHole);
                                        }
                                    }
                                    else
                                    {
                                        await _drillHoleRepository.UpdateAsync(drillHole);
                                    }

                                    break;
                            }
                        }

                        await _unitOfWorkManager.Current.SaveChangesAsync();
                    }
                }

                break;
            }
            default:
                throw new ArgumentOutOfRangeException();
        }

        if (errors.Count > 0)
        {
            return new BadRequestObjectResult(errors);
        }

        return new OkObjectResult(new
        {
            newDrillHoles = createdDrillHoles,
            countRecord
        });
    }

    /// <inheritdoc />
    public async Task<ValidationDataUploadDto> ValidateImportFileAsync(UploadDataDto input)
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        var importType = input.ImportFileType;
        var suiteId = input.SuiteId;
        var validationResult = new ValidationDataUploadDto();

        var importMappingTemplate = await _importMappingTemplateRepository
            .GetAllIncluding(x => x.ImportMappingTemplateFields)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == input.ImportMappingTemplateId);
        if (importMappingTemplate != null)
        {
            importType = importMappingTemplate.ImportFileType;
            suiteId = importMappingTemplate.SuiteId;
        }

        var rawImportMappingFields = input.ImportMappingFields;
        var importMappingFields =
            JsonConvert.DeserializeObject<List<ImportMappingFieldDto>>(rawImportMappingFields ?? "[]") ?? [];

        var mappingList = importMappingTemplate?.ImportMappingTemplateFields.ToList() ?? importMappingFields
            .Select(x => new ImportMappingTemplateField
            {
                SystemFieldName = x.SystemFieldName,
                FileColumnName = x.FileColumnName
            }).ToList();

        if (mappingList.Count == 0)
        {
            throw new UserFriendlyException("Mapping template list is required.");
        }

        var drillHoles = await _drillHoleRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectId == input.ProjectId && x.ProspectId == input.ProspectId)
            .ToListAsync();

        if (drillHoles.Count == 0 && input.ImportFileType != ImportFileType.DrillHole)
        {
            throw new UserFriendlyException("There are no drill holes in the Project.");
        }

        switch (importType)
        {
            case ImportFileType.Geology:
            {
                if (suiteId == null)
                {
                    throw new UserFriendlyException("SuiteId is required");
                }

                using (var stream = input.ExcelFile.OpenReadStream())
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];
                    if (worksheet.Dimension?.Rows > 1)
                    {
                        var gaps = new List<List<int>>();
                        var overlaps = new List<List<int>>();
                        var duplicates = new List<List<int>>();

                        // Assuming file structure has DrillHole, DepthFrom, DepthTo columns
                        for (var row = 2; row <= worksheet.Dimension.Rows; row++)
                        {
                            var currentDepthFrom = decimal.Parse(worksheet.Cells[row, 2].Text);
                            var currentDepthTo = decimal.Parse(worksheet.Cells[row, 3].Text);

                            if (row > 2)
                            {
                                var previousDepthTo = decimal.Parse(worksheet.Cells[row - 1, 3].Text);

                                // Check for gaps
                                if (currentDepthFrom > previousDepthTo)
                                {
                                    gaps.Add([row - 1, row]);
                                }
                                // Check for overlaps
                                else if (currentDepthFrom < previousDepthTo)
                                {
                                    overlaps.Add([row - 1, row]);
                                }
                                // Check for duplicates
                                else if (currentDepthFrom == previousDepthTo && currentDepthTo == previousDepthTo)
                                {
                                    duplicates.Add([row - 1, row]);
                                }
                            }
                        }

                        validationResult.Gap = gaps;
                        validationResult.Overlap = overlaps;
                        validationResult.Duplicate = duplicates;
                    }
                }

                break;
            }
            case ImportFileType.Geophysics:
            {
                if (suiteId == null)
                {
                    throw new UserFriendlyException("SuiteId is required");
                }

                validationResult.Gap = [];
                validationResult.Overlap = [];
                validationResult.Duplicate = [];
                break;
            }
            case ImportFileType.Assay:
            {
                if (suiteId == null)
                {
                    throw new UserFriendlyException("SuiteId is required");
                }

                // Similar validation as Geology for depth ranges
                using (var stream = input.ExcelFile.OpenReadStream())
                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets[0];
                    if (worksheet.Dimension?.Rows > 1)
                    {
                        var gaps = new List<List<int>>();
                        var overlaps = new List<List<int>>();
                        var duplicates = new List<List<int>>();

                        // Assuming file structure has DrillHole, DepthFrom, DepthTo columns
                        for (var row = 2; row <= worksheet.Dimension.Rows; row++)
                        {
                            var currentDepthFrom = decimal.Parse(worksheet.Cells[row, 2].Text);
                            var currentDepthTo = decimal.Parse(worksheet.Cells[row, 3].Text);

                            if (row > 2)
                            {
                                var previousDepthTo = decimal.Parse(worksheet.Cells[row - 1, 3].Text);

                                // Check for gaps
                                if (currentDepthFrom > previousDepthTo)
                                {
                                    gaps.Add([row - 1, row]);
                                }
                                // Check for overlaps
                                else if (currentDepthFrom < previousDepthTo)
                                {
                                    overlaps.Add([row - 1, row]);
                                }
                                // Check for duplicates
                                else if (currentDepthFrom == previousDepthTo && currentDepthTo == previousDepthTo)
                                {
                                    duplicates.Add([row - 1, row]);
                                }
                            }
                        }

                        validationResult.Gap = gaps;
                        validationResult.Overlap = overlaps;
                        validationResult.Duplicate = duplicates;
                    }
                }

                break;
            }
            case ImportFileType.DownholeSurvey:
            {
                validationResult.Gap = [];
                validationResult.Overlap = [];
                validationResult.Duplicate = [];
                break;
            }
            case ImportFileType.DrillHole:
                validationResult.Gap = [];
                validationResult.Overlap = [];
                validationResult.Duplicate = [];
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }

        return validationResult;
    }

    /// <summary>
    /// Deletes an import mapping template and all its associated fields
    /// </summary>
    /// <param name="input">The ID of the template to delete</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task DeleteTemplateAsync(EntityDto<int> input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var template = await _importMappingTemplateRepository.GetAsync(input.Id);
        if (template == null)
        {
            throw new UserFriendlyException("Template not found.");
        }

        if (template.TenantId != tenantId)
        {
            throw new UserFriendlyException("You don't have permission to delete this template.");
        }

        // Delete all associated template fields
        var fields = await _importMappingTemplateFieldRepository
            .GetAllListAsync(f => f.ImportMappingTemplateId == template.Id);

        foreach (var field in fields)
        {
            await _importMappingTemplateFieldRepository.DeleteAsync(field);
        }

        // Delete the template
        await _importMappingTemplateRepository.DeleteAsync(template);

        // Save all changes in a single transaction
        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <summary>
    /// Gets a typed value from an Excel cell
    /// </summary>
    private static T? GetCellValue<T>(ExcelWorksheet worksheet, int row, Dictionary<string, int> columnMappings,
        string fieldName)
    {
        if (!columnMappings.TryGetValue(fieldName, out var value))
            return default;

        var cellValue = worksheet.Cells[row, value].Text?.Trim();
        if (string.IsNullOrEmpty(cellValue))
            return default;

        try
        {
            if (typeof(T) == typeof(string))
                return (T)(object)cellValue;

            if (typeof(T) == typeof(decimal) || typeof(T) == typeof(decimal?))
                return (T)(object)decimal.Parse(cellValue);

            if (typeof(T) == typeof(double) || typeof(T) == typeof(double?))
                return (T)(object)double.Parse(cellValue);

            return default;
        }
        catch
        {
            return default;
        }
    }
}