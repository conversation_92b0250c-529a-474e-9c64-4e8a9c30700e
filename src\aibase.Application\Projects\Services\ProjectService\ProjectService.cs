using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.AssaySuites;
using aibase.AssaySuites.Dto;
using aibase.Authorization.Roles;
using aibase.Authorization.Users;
using aibase.GeologySuites;
using aibase.GeologySuites.Dto;
using aibase.GeotechSuites;
using aibase.GeotechSuites.Dto;
using aibase.ImageTypes;
using aibase.ImageTypes.Dto;
using aibase.LoggingViews;
using aibase.MobileProfiles;
using aibase.MobileProfiles.Dto;
using aibase.Polygons;
using aibase.Polygons.Dto;
using aibase.ProjectEntity;
using aibase.Projects.Dto;
using aibase.Projects.Services.AssignService;
using aibase.Prospects;
using aibase.Prospects.Dto;
using aibase.RockGroups.Dto;
using aibase.RqdCalculations;
using aibase.RqdCalculations.Dto;
using aibase.Suites;
using aibase.Suites.Dto;
using aibase.UserProjects;
using aibase.Workflows.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using AssignProjectGeotechSuiteDto = aibase.Projects.Dto.AssignProjectGeotechSuiteDto;

namespace aibase.Projects.Services.ProjectService;

/// <inheritdoc />
public class ProjectService : IProjectService
{
    private readonly UserManager _userManager;
    private readonly IRepository<Project, int> _repository;
    private readonly IRepository<UserProject, int> _userProjectRepository;
    private readonly IRepository<Polygon, int> _polygonRepository;
    private readonly IRepository<Suite, int> _suiteRepository;
    private readonly IRepository<GeologySuite, int> _geologySuiteRepository;
    private readonly IRepository<AssaySuite, int> _assaySuiteRepository;
    private readonly IRepository<Prospect, int> _prospectRepository;
    private readonly IRepository<LoggingView, int> _loggingRepository;
    private readonly IRepository<GeotechSuite, int> _geotechSuiteRepository;
    private readonly IRepository<RqdCalculation, int> _rqdCalculationRepository;
    private readonly IRepository<ImageType, int> _imageTypeRepository;
    private readonly IAssignService _assignService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly IRepository<MobileProfile, int> _mobileProfileRepository;

    /// <summary>
    /// 
    /// </summary>
    public ProjectService(
        UserManager userManager,
        IRepository<Project, int> repository,
        IRepository<UserProject, int> userProjectRepository,
        IRepository<Polygon, int> polygonRepository,
        IRepository<Suite, int> suiteRepository,
        IRepository<GeologySuite, int> geologySuiteRepository,
        IRepository<AssaySuite, int> assaySuiteRepository,
        IRepository<Prospect, int> prospectRepository,
        IRepository<LoggingView, int> loggingRepository, 
        IRepository<GeotechSuite, int> geotechSuiteRepository,
        IRepository<RqdCalculation, int> rqdCalculationRepository, 
        IRepository<ImageType, int> imageTypeRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper, 
        IAssignService assignService,
        IRepository<MobileProfile, int> mobileProfileRepository)
    {
        _userManager = userManager;
        _repository = repository;
        _userProjectRepository = userProjectRepository;
        _polygonRepository = polygonRepository;
        _suiteRepository = suiteRepository;
        _geologySuiteRepository = geologySuiteRepository;
        _assaySuiteRepository = assaySuiteRepository;
        _prospectRepository = prospectRepository;
        _assignService = assignService;
        _loggingRepository = loggingRepository;
        _geotechSuiteRepository = geotechSuiteRepository;
        _rqdCalculationRepository = rqdCalculationRepository;
        _imageTypeRepository = imageTypeRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
        _mobileProfileRepository = mobileProfileRepository;
    }

    /// <inheritdoc />
    public async Task<Project> CreateAsync(CreateProjectDto input)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var existingProject = await _repository.FirstOrDefaultAsync(x =>
            x.Name == input.Name && x.TenantId == _abpSession.GetTenantId());

        if (existingProject != null)
        {
            throw new UserFriendlyException($"Project with name '{input.Name}' already exists.");
        }

        // Validate MobileProfileId if provided
        if (input.MobileProfileId.HasValue)
        {
            await ValidateMobileProfileExists(input.MobileProfileId.Value);
        }

        var project = new Project
        {
            Name = input.Name,
            Code = input.Code,
            CoreTrayLength = input.CoreTrayLength,
            Description = input.Description,
            BackgroundColor = input.BackgroundColor,
            TextColor = input.TextColor,
            LoggingTextColor = input.LoggingTextColor,
            BoundingBoxIds = string.Join(",", input.BoundingBoxId),
            BoundingRowsIds = string.Join(",", input.BoundingRowsId),
            IsActive = input.IsActive,
            WorkflowId = input.MobileWorkflowId ?? null,
            RockGroupId = input.RockGroupId,
            MobileProfileId = input.MobileProfileId,
            TenantId = _abpSession.GetTenantId(),
        };

        await _repository.InsertAsync(project);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        if (input.LoggingViewIds is { Count: > 0 })
        {
            var assignProjectLoggingViewDto = new AssignProjectLoggingViewDto
            {
                ProjectId = project.Id,
                LoggingViewIds = input.LoggingViewIds
            };
            await _assignService.AssignLoggingViewAsync(assignProjectLoggingViewDto);
        }

        if (input.SuiteIds is { Count: > 0 })
        {
            var assignSuiteProject = new AssignSuiteProject
            {
                ProjectId = project.Id,
                SuiteIds = input.SuiteIds
            };
            await _assignService.AssignSuiteAsync(assignSuiteProject);
        }

        if (input.AssaySuiteIds is { Count: > 0 })
        {
            var assignAssaySuiteDto = new AssignAssaySuiteDto
            {
                ProjectId = project.Id,
                AssaySuiteIds = input.AssaySuiteIds
            };
            await _assignService.AssignAssaySuiteAsync(assignAssaySuiteDto);
        }

        if (input.GeologySuiteIds is { Count: > 0 })
        {
            var assignGeologyProjectSuiteDto = new AssignGeologyProjectSuiteDto
            {
                ProjectId = project.Id,
                GeologySuiteIds = input.GeologySuiteIds
            };
            await _assignService.AssignGeologySuiteAsync(assignGeologyProjectSuiteDto);
        }

        if (input.GeotechSuiteIds is { Count: > 0 })
        {
            var assignProjectGeotechSuiteDto = new AssignProjectGeotechSuiteDto
            {
                ProjectId = project.Id,
                GeotechSuiteIds = input.GeotechSuiteIds
            };
            await _assignService.AssignGeotechSuiteAsync(assignProjectGeotechSuiteDto);
        }

        if (input.RqdCalculationIds is { Count: > 0 })
        {
            var assignProjectRqdCalculationDto = new AssignProjectRqdCalculationDto
            {
                ProjectId = project.Id,
                RqdCalculationIds = input.RqdCalculationIds
            };
            await _assignService.AssignRqdCalculationAsync(assignProjectRqdCalculationDto);
        }

        if (input.ImageTypeIds is { Count: > 0 })
        {
            var assignProjectRqdCalculationDto = new AssignProjectImageTypeDto
            {
                ProjectId = project.Id,
                ImageTypeIds = input.ImageTypeIds
            };
            await _assignService.AssignImageTypeAsync(assignProjectRqdCalculationDto);
        }

        return project;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ProjectDto>> GetAllAsync(PagedProjectResultRequestDto input)
    {
        IList<string> roles;
        User? currentUser;
        using (_unitOfWorkManager.Current.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            currentUser = await _userManager.GetUserByIdAsync(_abpSession.GetUserId());
            roles = await _userManager.GetRolesAsync(currentUser);
        }

        IQueryable<Project> query;
        if (roles.Contains(StaticRoleNames.Tenants.Admin) || roles.Contains(StaticRoleNames.Host.Admin))
        {
            query = _repository.GetAllIncluding(x => x.Workflow, x => x.RockGroup)
                .AsNoTracking()
                .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
                .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
                .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                    x => input.Keyword != null && (x.Name.ToLower().Contains(input.Keyword.ToLower()) ||
                                                   x.Description.ToLower().Contains(input.Keyword.ToLower())));
        }
        else
        {
            query = _userProjectRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.userId == currentUser.Id)
                .Select(x => x.Project)
                .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
                .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
                .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                    x => input.Keyword != null && (x.Name.ToLower().Contains(input.Keyword.ToLower()) ||
                                                   x.Description.ToLower().Contains(input.Keyword.ToLower())));
        }

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.SortField) && !string.IsNullOrWhiteSpace(input.SortOrder))
        {
            query = query.ApplySorting(input.SortField, input.SortOrder, r => r.CreationTime);
        }
        else
        {
            query = query.OrderByDescending(x => x.CreationTime); // Default sorting
        }

        var totalCount = query.Count();

        var project = query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToList();

        var projectsDto = _mapper.Map<List<ProjectDto>>(project);

        foreach (var projectDto in projectsDto)
        {
            if (!string.IsNullOrWhiteSpace(projectDto.BoundingBoxIds))
            {
                var boundingBoxIds = projectDto.BoundingBoxIds.Split(',').Select(int.Parse).ToList();

                projectDto.BoundingBoxIdArr = boundingBoxIds;
                projectDto.BoundingBoxs = await GetPolygonsByIdsAsync(boundingBoxIds);
            }

            if (!string.IsNullOrWhiteSpace(projectDto.BoundingRowsIds))
            {
                var boundingRowsIds = projectDto.BoundingRowsIds.Split(',').Select(int.Parse).ToList();

                projectDto.BoundingRowsIdAr = boundingRowsIds;
                projectDto.BoundingRows = await GetPolygonsByIdsAsync(boundingRowsIds);
            }
        }

        return new PagedResultDto<ProjectDto>(totalCount, projectsDto);
    }

    /// <inheritdoc />
    public async Task<ProjectDto> GetAsync(EntityDto<int> input)
    {
        var project = await ValidateProjectEntity(input.Id);

        var relateGeology = await _geologySuiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.GeologyProjectSuites.Any(y => y.ProjectId == project.Id))
            .Select(x => new GeologySuiteDto()
            {
                Id = x.Id,
                Name = x.Name,
            }).ToListAsync();

        var relateGeophysics = await _suiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.SuiteProjects.Any(y => y.ProjectId == project.Id))
            .Select(x => new SuiteDto()
            {
                Id = x.Id,
                Name = x.Name,
            }).ToListAsync();

        var relateAssay = await _assaySuiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.AssayProjectSuites.Any(y => y.ProjectId == project.Id))
            .Select(x => new AssaySuiteDto()
            {
                Id = x.Id,
                Name = x.Name,
            }).ToListAsync();

        var relateLoggingView = await _loggingRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectLoggingViews.Any(y => y.ProjectId == project.Id))
            .Select(x => new LoggingView
            {
                Id = x.Id,
                Name = x.Name,
            }).ToListAsync();

        var relateGeotechSuite = await _geotechSuiteRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectGeotechSuites.Any(y => y.ProjectId == project.Id))
            .Select(x => _mapper.Map<GeotechSuiteDto>(x))
            .ToListAsync();

        var relateRqdCalculation = await _rqdCalculationRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.RqdCalculationProjects.Any(y => y.ProjectId == project.Id))
            .Select(x => _mapper.Map<RqdCalculationDto>(x))
            .ToListAsync();
        
        var relateImageType = await _imageTypeRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ProjectImageTypes.Any(y => y.ProjectId == project.Id))
            .Select(x => _mapper.Map<ImageTypeDto>(x))
            .ToListAsync();

        var projectDto = new ProjectDto
        {
            Id = project.Id,
            Name = project.Name,
            Code = project.Code,
            CoreTrayLength = project.CoreTrayLength,
            Description = project.Description,
            BackgroundColor = project.BackgroundColor,
            TextColor = project.TextColor,
            LoggingTextColor = project.LoggingTextColor,
            BoundingBoxIds = project.BoundingBoxIds,
            BoundingRowsIds = project.BoundingRowsIds,
            IsActive = project.IsActive,
            GeologySuites = relateGeology,
            GeophysicsSuites = relateGeophysics,
            AssaySuites = relateAssay,
            LoggingViews = relateLoggingView,
            GeotechSuites = relateGeotechSuite,
            RqdCalculations = relateRqdCalculation,
            ImageTypes = relateImageType,
            RockGroup = _mapper.Map<RockGroupDto>(project.RockGroup),
            Workflow = _mapper.Map<WorkflowDto>(project.Workflow),
            MobileProfile = project.MobileProfile != null ? new MobileProfileDto
            {
                Id = project.MobileProfile.Id,
                Name = project.MobileProfile.Name,
                Description = project.MobileProfile.Description,
                IsActive = project.MobileProfile.IsActive,
                IsStandard = project.MobileProfile.IsStandard,
                IsWet = project.MobileProfile.IsWet,
                IsDry = project.MobileProfile.IsDry,
                IsUv = project.MobileProfile.IsUv,
                IsRig = project.MobileProfile.IsRig,
                MobileCameraType = project.MobileProfile.MobileCameraType,
                ExternalCameraType = project.MobileProfile.ExternalCameraType,
                IsDepthIncrement = project.MobileProfile.IsDepthIncrement,
                DepthIncrement = project.MobileProfile.DepthIncrement,
                IsApplyDepthIncrement = project.MobileProfile.IsApplyDepthIncrement,
                RotateImgMobile = project.MobileProfile.RotateImgMobile,
                RotateImgExternal = project.MobileProfile.RotateImgExternal
            } : null,
        };

        if (!string.IsNullOrWhiteSpace(projectDto.BoundingBoxIds))
        {
            var boundingBoxIds = projectDto.BoundingBoxIds.Split(',').Select(int.Parse).ToList();
            projectDto.BoundingBoxIdArr = boundingBoxIds;
            projectDto.BoundingBoxs = await GetPolygonsByIdsAsync(boundingBoxIds);
        }

        if (!string.IsNullOrWhiteSpace(projectDto.BoundingRowsIds))
        {
            var boundingRowsIds = projectDto.BoundingRowsIds.Split(',').Select(int.Parse).ToList();
            projectDto.BoundingRowsIdAr = boundingRowsIds;
            projectDto.BoundingRows = await GetPolygonsByIdsAsync(boundingRowsIds);
        }

        return projectDto;
    }

    /// <inheritdoc />
    public async Task<ProjectDto> UpdateAsync(UpdateProjectDto input)
    {
        var project = await ValidateProjectEntity(input.Id);

        // Check for duplicate name if name is being changed
        if (!string.IsNullOrEmpty(input.Name) && input.Name != project.Name)
        {
            var existingProject = await _repository.FirstOrDefaultAsync(x =>
                x.Name == input.Name && x.TenantId == _abpSession.GetTenantId() && x.Id != input.Id);

            if (existingProject != null)
            {
                throw new UserFriendlyException($"Project with name '{input.Name}' already exists.");
            }
        }

        // Validate MobileProfileId if provided
        if (input.MobileProfileId.HasValue)
        {
            await ValidateMobileProfileExists(input.MobileProfileId.Value);
        }

        // Update properties
        project.Name = input.Name ?? project.Name;
        project.Code = input.Code ?? project.Code;
        project.CoreTrayLength = input.CoreTrayLength ?? project.CoreTrayLength;
        project.Description = input.Description ?? project.Description;
        project.BackgroundColor = input.BackgroundColor ?? project.BackgroundColor;
        project.TextColor = input.TextColor ?? project.TextColor;
        project.LoggingTextColor = input.LoggingTextColor ?? project.LoggingTextColor;
        project.BoundingBoxIds = string.Join(",", input.BoundingBoxId!) ?? project.BoundingBoxIds;
        project.BoundingRowsIds = string.Join(",", input.BoundingRowsId!) ?? project.BoundingRowsIds;
        project.IsActive = input.IsActive ?? project.IsActive;
        project.WorkflowId = input.MobileWorkflowId ?? project.WorkflowId;
        project.RockGroupId = input.RockGroupId ?? project.RockGroupId;
        project.MobileProfileId = input.MobileProfileId;

        if (input.LoggingViewIds != null)
        {
            var assignProjectLoggingViewDto = new AssignProjectLoggingViewDto
            {
                ProjectId = project.Id,
                LoggingViewIds = input.LoggingViewIds
            };
            await _assignService.AssignLoggingViewAsync(assignProjectLoggingViewDto);
        }

        if (input.SuiteIds != null)
        {
            var assignSuiteProject = new AssignSuiteProject
            {
                ProjectId = project.Id,
                SuiteIds = input.SuiteIds
            };
            await _assignService.AssignSuiteAsync(assignSuiteProject);
        }

        if (input.AssaySuiteIds != null)
        {
            var assignAssaySuiteDto = new AssignAssaySuiteDto
            {
                ProjectId = project.Id,
                AssaySuiteIds = input.AssaySuiteIds
            };
            await _assignService.AssignAssaySuiteAsync(assignAssaySuiteDto);
        }

        if (input.GeologySuiteIds != null)
        {
            var assignGeologyProjectSuiteDto = new AssignGeologyProjectSuiteDto
            {
                ProjectId = project.Id,
                GeologySuiteIds = input.GeologySuiteIds
            };
            await _assignService.AssignGeologySuiteAsync(assignGeologyProjectSuiteDto);
        }

        if (input.GeotechSuiteIds != null)
        {
            var assignProjectGeotechSuiteDto = new AssignProjectGeotechSuiteDto
            {
                ProjectId = project.Id,
                GeotechSuiteIds = input.GeotechSuiteIds
            };
            await _assignService.AssignGeotechSuiteAsync(assignProjectGeotechSuiteDto);
        }

        if (input.RqdCalculationIds != null)
        {
            var assignProjectRqdCalculationDto = new AssignProjectRqdCalculationDto
            {
                ProjectId = project.Id,
                RqdCalculationIds = input.RqdCalculationIds
            };
            await _assignService.AssignRqdCalculationAsync(assignProjectRqdCalculationDto);
        }

        if (input.ImageTypeIds != null)
        {
            var assignProjectRqdCalculationDto = new AssignProjectImageTypeDto
            {
                ProjectId = project.Id,
                ImageTypeIds = input.ImageTypeIds
            };
            await _assignService.AssignImageTypeAsync(assignProjectRqdCalculationDto);
        }

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        await ValidateProjectEntity(input.Id);
        await _repository.DeleteAsync(input.Id);
    }

    /// <inheritdoc />
    public async Task<ProjectProspectDto> GetContext()
    {
        var tenantId = _abpSession.GetTenantId();
        var queryProject = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == tenantId);
        var totalProject = await queryProject.CountAsync();

        var queryProspect = _prospectRepository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == tenantId);
        var totalProspect = await queryProspect.CountAsync();

        return new ProjectProspectDto()
        {
            Projects = new PagedResultDto<ProjectDto>(totalProject,
                _mapper.Map<List<ProjectDto>>(queryProject.ToList())),
            Prospects = new PagedResultDto<ProspectDto>(totalProspect,
                _mapper.Map<List<ProspectDto>>(queryProspect.ToList()))
        };
    }

    private async Task<List<PolygonDto>> GetPolygonsByIdsAsync(List<int> ids)
    {
        var polygons = await _polygonRepository.GetAll()
            .Where(p => ids.Contains(p.Id))
            .ToListAsync();

        return _mapper.Map<List<PolygonDto>>(polygons);
    }

    private async Task<Project> ValidateProjectEntity(int id)
    {
        var project = await _repository.GetAllIncluding(x => x.Workflow, x => x.RockGroup, x => x.MobileProfile)
            .FirstOrDefaultAsync(x => x.Id == id && x.TenantId == _abpSession.GetTenantId());
        if (project == null)
        {
            throw new EntityNotFoundException(typeof(Project), id);
        }

        return project;
    }

    private async Task ValidateMobileProfileExists(int mobileProfileId)
    {
        var mobileProfile = await _mobileProfileRepository.FirstOrDefaultAsync(mp => 
            mp.Id == mobileProfileId && mp.TenantId == _abpSession.GetTenantId());
        
        if (mobileProfile == null)
        {
            throw new EntityNotFoundException(typeof(MobileProfile), mobileProfileId);
        }
    }
}
