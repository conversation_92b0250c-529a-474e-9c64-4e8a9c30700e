﻿// <auto-generated />
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using aibase.EntityFrameworkCore;

#nullable disable

namespace aibase.Migrations
{
    [DbContext(typeof(aibaseDbContext))]
    partial class aibaseDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Abp.Application.Editions.Edition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DeleterUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.HasKey("Id");

                    b.ToTable("AbpEditions");
                });

            modelBuilder.Entity("Abp.Application.Features.FeatureSetting", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(21)
                        .HasColumnType("character varying(21)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.HasKey("Id");

                    b.ToTable("AbpFeatures");

                    b.HasDiscriminator<string>("Discriminator").HasValue("FeatureSetting");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Abp.Auditing.AuditLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("BrowserInfo")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("ClientIpAddress")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("ClientName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("CustomData")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Exception")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("ExceptionMessage")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<int>("ExecutionDuration")
                        .HasColumnType("integer");

                    b.Property<DateTime>("ExecutionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ImpersonatorTenantId")
                        .HasColumnType("integer");

                    b.Property<long?>("ImpersonatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("MethodName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Parameters")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("ReturnValue")
                        .HasColumnType("text");

                    b.Property<string>("ServiceName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long?>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "ExecutionDuration");

                    b.HasIndex("TenantId", "ExecutionTime");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("AbpAuditLogs");
                });

            modelBuilder.Entity("Abp.Authorization.PermissionSetting", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasMaxLength(21)
                        .HasColumnType("character varying(21)");

                    b.Property<bool>("IsGranted")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name");

                    b.ToTable("AbpPermissions");

                    b.HasDiscriminator<string>("Discriminator").HasValue("PermissionSetting");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Abp.Authorization.Roles.RoleClaim", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ClaimType")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.HasIndex("TenantId", "ClaimType");

                    b.ToTable("AbpRoleClaims");
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserAccount", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DeleterUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EmailAddress")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("UserLinkId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("EmailAddress");

                    b.HasIndex("UserName");

                    b.HasIndex("TenantId", "EmailAddress");

                    b.HasIndex("TenantId", "UserId");

                    b.HasIndex("TenantId", "UserName");

                    b.ToTable("AbpUserAccounts");
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserClaim", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("ClaimType")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("TenantId", "ClaimType");

                    b.ToTable("AbpUserClaims");
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserLogin", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("LoginProvider")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("ProviderKey")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("ProviderKey", "TenantId")
                        .IsUnique();

                    b.HasIndex("TenantId", "UserId");

                    b.HasIndex("TenantId", "LoginProvider", "ProviderKey");

                    b.ToTable("AbpUserLogins");
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserLoginAttempt", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("BrowserInfo")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("ClientIpAddress")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("ClientName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<byte>("Result")
                        .HasColumnType("smallint");

                    b.Property<string>("TenancyName")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long?>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("UserNameOrEmailAddress")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("UserId", "TenantId");

                    b.HasIndex("TenancyName", "UserNameOrEmailAddress", "Result");

                    b.ToTable("AbpUserLoginAttempts");
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserOrganizationUnit", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<long>("OrganizationUnitId")
                        .HasColumnType("bigint");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "OrganizationUnitId");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("AbpUserOrganizationUnits");
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserRole", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("TenantId", "RoleId");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("AbpUserRoles");
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserToken", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime?>("ExpireDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Value")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("AbpUserTokens");
                });

            modelBuilder.Entity("Abp.BackgroundJobs.BackgroundJobInfo", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsAbandoned")
                        .HasColumnType("boolean");

                    b.Property<string>("JobArgs")
                        .IsRequired()
                        .HasMaxLength(1048576)
                        .HasColumnType("character varying(1048576)");

                    b.Property<string>("JobType")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<DateTime?>("LastTryTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("NextTryTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<byte>("Priority")
                        .HasColumnType("smallint");

                    b.Property<short>("TryCount")
                        .HasColumnType("smallint");

                    b.HasKey("Id");

                    b.HasIndex("IsAbandoned", "NextTryTime");

                    b.ToTable("AbpBackgroundJobs");
                });

            modelBuilder.Entity("Abp.Configuration.Setting", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long?>("UserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("TenantId", "Name", "UserId")
                        .IsUnique();

                    b.ToTable("AbpSettings");
                });

            modelBuilder.Entity("Abp.DynamicEntityProperties.DynamicEntityProperty", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("DynamicPropertyId")
                        .HasColumnType("integer");

                    b.Property<string>("EntityFullName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DynamicPropertyId");

                    b.HasIndex("EntityFullName", "DynamicPropertyId", "TenantId")
                        .IsUnique();

                    b.ToTable("AbpDynamicEntityProperties");
                });

            modelBuilder.Entity("Abp.DynamicEntityProperties.DynamicEntityPropertyValue", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("DynamicEntityPropertyId")
                        .HasColumnType("integer");

                    b.Property<string>("EntityId")
                        .HasColumnType("text");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DynamicEntityPropertyId");

                    b.ToTable("AbpDynamicEntityPropertyValues");
                });

            modelBuilder.Entity("Abp.DynamicEntityProperties.DynamicProperty", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("DisplayName")
                        .HasColumnType("text");

                    b.Property<string>("InputType")
                        .HasColumnType("text");

                    b.Property<string>("Permission")
                        .HasColumnType("text");

                    b.Property<string>("PropertyName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PropertyName", "TenantId")
                        .IsUnique();

                    b.ToTable("AbpDynamicProperties");
                });

            modelBuilder.Entity("Abp.DynamicEntityProperties.DynamicPropertyValue", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("DynamicPropertyId")
                        .HasColumnType("integer");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DynamicPropertyId");

                    b.ToTable("AbpDynamicPropertyValues");
                });

            modelBuilder.Entity("Abp.EntityHistory.EntityChange", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("ChangeTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<byte>("ChangeType")
                        .HasColumnType("smallint");

                    b.Property<long>("EntityChangeSetId")
                        .HasColumnType("bigint");

                    b.Property<string>("EntityId")
                        .HasMaxLength(48)
                        .HasColumnType("character varying(48)");

                    b.Property<string>("EntityTypeFullName")
                        .HasMaxLength(192)
                        .HasColumnType("character varying(192)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("EntityChangeSetId");

                    b.HasIndex("EntityTypeFullName", "EntityId");

                    b.ToTable("AbpEntityChanges");
                });

            modelBuilder.Entity("Abp.EntityHistory.EntityChangeSet", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("BrowserInfo")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("ClientIpAddress")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("ClientName")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExtensionData")
                        .HasColumnType("text");

                    b.Property<int?>("ImpersonatorTenantId")
                        .HasColumnType("integer");

                    b.Property<long?>("ImpersonatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Reason")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long?>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "CreationTime");

                    b.HasIndex("TenantId", "Reason");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("AbpEntityChangeSets");
                });

            modelBuilder.Entity("Abp.EntityHistory.EntityPropertyChange", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<long>("EntityChangeId")
                        .HasColumnType("bigint");

                    b.Property<string>("NewValue")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("NewValueHash")
                        .HasColumnType("text");

                    b.Property<string>("OriginalValue")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("OriginalValueHash")
                        .HasColumnType("text");

                    b.Property<string>("PropertyName")
                        .HasMaxLength(96)
                        .HasColumnType("character varying(96)");

                    b.Property<string>("PropertyTypeFullName")
                        .HasMaxLength(192)
                        .HasColumnType("character varying(192)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("EntityChangeId");

                    b.ToTable("AbpEntityPropertyChanges");
                });

            modelBuilder.Entity("Abp.Localization.ApplicationLanguage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DeleterUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("Icon")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDisabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name");

                    b.ToTable("AbpLanguages");
                });

            modelBuilder.Entity("Abp.Localization.ApplicationLanguageText", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("LanguageName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(67108864)
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Source", "LanguageName", "Key");

                    b.ToTable("AbpLanguageTexts");
                });

            modelBuilder.Entity("Abp.Notifications.NotificationInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Data")
                        .HasMaxLength(1048576)
                        .HasColumnType("character varying(1048576)");

                    b.Property<string>("DataTypeName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("EntityId")
                        .HasMaxLength(96)
                        .HasColumnType("character varying(96)");

                    b.Property<string>("EntityTypeAssemblyQualifiedName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("EntityTypeName")
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("ExcludedUserIds")
                        .HasMaxLength(131072)
                        .HasColumnType("character varying(131072)");

                    b.Property<string>("NotificationName")
                        .IsRequired()
                        .HasMaxLength(96)
                        .HasColumnType("character varying(96)");

                    b.Property<byte>("Severity")
                        .HasColumnType("smallint");

                    b.Property<string>("TargetNotifiers")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("TenantIds")
                        .HasMaxLength(131072)
                        .HasColumnType("character varying(131072)");

                    b.Property<string>("UserIds")
                        .HasMaxLength(131072)
                        .HasColumnType("character varying(131072)");

                    b.HasKey("Id");

                    b.ToTable("AbpNotifications");
                });

            modelBuilder.Entity("Abp.Notifications.NotificationSubscriptionInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("EntityId")
                        .HasMaxLength(96)
                        .HasColumnType("character varying(96)");

                    b.Property<string>("EntityTypeAssemblyQualifiedName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("EntityTypeName")
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("NotificationName")
                        .HasMaxLength(96)
                        .HasColumnType("character varying(96)");

                    b.Property<string>("TargetNotifiers")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("NotificationName", "EntityTypeName", "EntityId", "UserId");

                    b.HasIndex("TenantId", "NotificationName", "EntityTypeName", "EntityId", "UserId");

                    b.ToTable("AbpNotificationSubscriptions");
                });

            modelBuilder.Entity("Abp.Notifications.TenantNotificationInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Data")
                        .HasMaxLength(1048576)
                        .HasColumnType("character varying(1048576)");

                    b.Property<string>("DataTypeName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("EntityId")
                        .HasMaxLength(96)
                        .HasColumnType("character varying(96)");

                    b.Property<string>("EntityTypeAssemblyQualifiedName")
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("EntityTypeName")
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("NotificationName")
                        .IsRequired()
                        .HasMaxLength(96)
                        .HasColumnType("character varying(96)");

                    b.Property<byte>("Severity")
                        .HasColumnType("smallint");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AbpTenantNotifications");
                });

            modelBuilder.Entity("Abp.Notifications.UserNotificationInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("State")
                        .HasColumnType("integer");

                    b.Property<string>("TargetNotifiers")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<Guid>("TenantNotificationId")
                        .HasColumnType("uuid");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("UserId", "State", "CreationTime");

                    b.ToTable("AbpUserNotifications");
                });

            modelBuilder.Entity("Abp.Organizations.OrganizationUnit", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(95)
                        .HasColumnType("character varying(95)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DeleterUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("ParentId")
                        .HasColumnType("bigint");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("TenantId", "Code");

                    b.ToTable("AbpOrganizationUnits");
                });

            modelBuilder.Entity("Abp.Organizations.OrganizationUnitRole", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<long>("OrganizationUnitId")
                        .HasColumnType("bigint");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "OrganizationUnitId");

                    b.HasIndex("TenantId", "RoleId");

                    b.ToTable("AbpOrganizationUnitRoles");
                });

            modelBuilder.Entity("Abp.Webhooks.WebhookEvent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Data")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("WebhookName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AbpWebhookEvents");
                });

            modelBuilder.Entity("Abp.Webhooks.WebhookSendAttempt", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Response")
                        .HasColumnType("text");

                    b.Property<int?>("ResponseStatusCode")
                        .HasColumnType("integer");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<Guid>("WebhookEventId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("WebhookSubscriptionId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("WebhookEventId");

                    b.ToTable("AbpWebhookSendAttempts");
                });

            modelBuilder.Entity("Abp.Webhooks.WebhookSubscriptionInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Headers")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Secret")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("WebhookUri")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Webhooks")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AbpWebhookSubscriptions");
                });

            modelBuilder.Entity("aibase.AIServiceEntity.AIService", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Endpoint")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SubscriptionKey")
                        .HasColumnType("text");

                    b.Property<string>("Thumbnail")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AbpAIServices");
                });

            modelBuilder.Entity("aibase.APIKeys.APIKey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastUsedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("TenantId");

                    b.ToTable("AbpAPIKeys");
                });

            modelBuilder.Entity("aibase.APIKeys.APIKeyRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ApiKeyId")
                        .HasColumnType("integer");

                    b.Property<int>("ApiKeyTypeId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ApiKeyId");

                    b.HasIndex("ApiKeyTypeId");

                    b.ToTable("AbpAPIKeyRoles");
                });

            modelBuilder.Entity("aibase.APIKeys.APIKeyTypes", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("Code")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Prefix")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AbpAPIKeyTypes");
                });

            modelBuilder.Entity("aibase.AssayAttributes.AssayAttribute", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("character varying(4)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AbpAssayAttributes");
                });

            modelBuilder.Entity("aibase.AssayDatas.AssayData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AssaySuiteId")
                        .HasColumnType("integer");

                    b.Property<string>("AttributeName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AttributeValue")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DrillHole")
                        .HasColumnType("text");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<string>("GroupId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssaySuiteId");

                    b.ToTable("AbpAssayData");
                });

            modelBuilder.Entity("aibase.AssayProjectSuites.AssayProjectSuite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AssaySuiteId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssaySuiteId");

                    b.HasIndex("ProjectId");

                    b.ToTable("AbpAssayProjectSuites");
                });

            modelBuilder.Entity("aibase.AssaySuiteAttributes.AssaySuiteAttribute", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AssayAttributeId")
                        .HasColumnType("integer");

                    b.Property<int>("AssaySuiteId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AssayAttributeId");

                    b.HasIndex("AssaySuiteId");

                    b.ToTable("AbpAssaySuiteAttributes");
                });

            modelBuilder.Entity("aibase.AssaySuiteFields.AssaySuiteField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AssayAttributeId")
                        .HasColumnType("integer");

                    b.Property<int>("AssaySuiteId")
                        .HasColumnType("integer");

                    b.Property<string>("BackgroundColour")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Sequence")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColour")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AssayAttributeId");

                    b.HasIndex("AssaySuiteId");

                    b.ToTable("AbpAssaySuiteFields");
                });

            modelBuilder.Entity("aibase.AssaySuites.AssaySuite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpAssaySuites");
                });

            modelBuilder.Entity("aibase.AssayTranslations.AssayTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<double>("ChangeTo")
                        .HasColumnType("double precision");

                    b.Property<string>("Character")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpAssayTranslations");
                });

            modelBuilder.Entity("aibase.Attributes.Attribute", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("MaxValue")
                        .HasColumnType("double precision");

                    b.Property<double>("MinInterval")
                        .HasColumnType("double precision");

                    b.Property<double>("MinValue")
                        .HasColumnType("double precision");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AbpAttributes");
                });

            modelBuilder.Entity("aibase.Authorization.Roles.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DeleterUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasMaxLength(5000)
                        .HasColumnType("character varying(5000)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsStatic")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatorUserId");

                    b.HasIndex("DeleterUserId");

                    b.HasIndex("LastModifierUserId");

                    b.HasIndex("TenantId", "NormalizedName");

                    b.ToTable("AbpRoles");
                });

            modelBuilder.Entity("aibase.Authorization.Users.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ReplacedByToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AbpRefreshTokens");
                });

            modelBuilder.Entity("aibase.Authorization.Users.User", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("AuthenticationSource")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DeleterUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EmailAddress")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EmailConfirmationCode")
                        .HasMaxLength(328)
                        .HasColumnType("character varying(328)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsLockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsTwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LockoutEndDateUtc")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("NormalizedEmailAddress")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("PasswordResetCode")
                        .HasMaxLength(328)
                        .HasColumnType("character varying(328)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("ProspectId")
                        .HasColumnType("integer");

                    b.Property<string>("SecurityStamp")
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("Surname")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("UserRoleConfigId")
                        .HasColumnType("integer");

                    b.Property<int?>("WorkRoleId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("CreatorUserId");

                    b.HasIndex("DeleterUserId");

                    b.HasIndex("LastModifierUserId");

                    b.HasIndex("UserRoleConfigId");

                    b.HasIndex("WorkRoleId");

                    b.HasIndex("TenantId", "NormalizedEmailAddress");

                    b.HasIndex("TenantId", "NormalizedUserName");

                    b.ToTable("AbpUsers");
                });

            modelBuilder.Entity("aibase.ColourValues.ColourValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("ColourId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<int>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ColourId");

                    b.HasIndex("GeologySuiteFieldId");

                    b.ToTable("AbpColourValues");
                });

            modelBuilder.Entity("aibase.Colours.Colour", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("HexCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpColours");
                });

            modelBuilder.Entity("aibase.CoordinatePolygons.CoordinatePolygon", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("Height")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("PolygonId")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<double>("Width")
                        .HasColumnType("double precision");

                    b.Property<double>("X")
                        .HasColumnType("double precision");

                    b.Property<double>("Y")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("PolygonId");

                    b.ToTable("AbpCoordinatePolygons");
                });

            modelBuilder.Entity("aibase.DataEntries.DataEntry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double?>("DepthFrom")
                        .HasColumnType("double precision");

                    b.Property<double?>("DepthTo")
                        .HasColumnType("double precision");

                    b.Property<int>("DrillholeId")
                        .HasColumnType("integer");

                    b.Property<int>("GeologySuiteId")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageCropId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double?>("X")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("DrillholeId");

                    b.HasIndex("GeologySuiteId");

                    b.ToTable("AbpDataEntries");
                });

            modelBuilder.Entity("aibase.DesurveyResults.DesurveyResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("Depth")
                        .HasColumnType("double precision");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<double>("Easting")
                        .HasColumnType("double precision");

                    b.Property<double>("Elevation")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("Northing")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("DrillHoleId");

                    b.ToTable("AbpDesurveyResults");
                });

            modelBuilder.Entity("aibase.DownholeDatas.DownholeData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AttributeName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AttributeValue")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DrillHole")
                        .HasColumnType("text");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<string>("GroupId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("SuiteId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("SuiteId");

                    b.ToTable("AbpDownholeDatas");
                });

            modelBuilder.Entity("aibase.DownholeSurveys.DownholeSurvey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<double>("Azimuth")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("Depth")
                        .HasColumnType("double precision");

                    b.Property<double>("Dip")
                        .HasColumnType("double precision");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("ProspectId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AbpDownholeSurveys");
                });

            modelBuilder.Entity("aibase.DownholeSurveys.DownholeSurveyType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpDownholeSurveyTypes");
                });

            modelBuilder.Entity("aibase.DrillHoleEntity.DrillHole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<decimal?>("Azimuth")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CroppedRows")
                        .HasColumnType("integer");

                    b.Property<decimal?>("Dip")
                        .HasColumnType("numeric");

                    b.Property<int?>("DrillHoleStatus")
                        .HasColumnType("integer");

                    b.Property<decimal?>("Easting")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Elevation")
                        .HasColumnType("numeric");

                    b.Property<int?>("ExportTemplateId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsExport")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal?>("Latitude")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("Longitude")
                        .HasColumnType("numeric");

                    b.Property<double>("MaxDepth")
                        .HasColumnType("double precision");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("Northing")
                        .HasColumnType("numeric");

                    b.Property<int>("OriginalImages")
                        .HasColumnType("integer");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("ProspectId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExportTemplateId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ProspectId");

                    b.ToTable("AbpDrillHoles");
                });

            modelBuilder.Entity("aibase.ExportEvents.ExportEvent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ExportTemplateId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ExportTemplateId");

                    b.ToTable("AbpExportEvents");
                });

            modelBuilder.Entity("aibase.ExportTemplateDrillHoles.ExportTemplateDrillHole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<int>("ExportTemplateId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DrillHoleId");

                    b.HasIndex("ExportTemplateId");

                    b.ToTable("AbpExportTemplateDrillHoles");
                });

            modelBuilder.Entity("aibase.ExportTemplateImageSubtypes.ExportTemplateImageSubtype", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ExportTemplateId")
                        .HasColumnType("integer");

                    b.Property<int>("ImageSubtypeId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExportTemplateId");

                    b.HasIndex("ImageSubtypeId");

                    b.ToTable("AbpExportTemplateImageSubtypes");
                });

            modelBuilder.Entity("aibase.ExportTemplateImageTypes.ExportTemplateImageType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ExportTemplateId")
                        .HasColumnType("integer");

                    b.Property<int>("ImageTypeId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ExportTemplateId");

                    b.HasIndex("ImageTypeId");

                    b.ToTable("AbpExportTemplateImageTypes");
                });

            modelBuilder.Entity("aibase.ExportTemplates.ExportTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataOutput")
                        .HasColumnType("integer");

                    b.Property<int>("DataSource")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("DrillHoleStatus")
                        .HasColumnType("integer");

                    b.Property<List<int>>("DrillholeIds")
                        .HasColumnType("integer[]");

                    b.Property<double?>("FileSize")
                        .HasColumnType("double precision");

                    b.Property<int[]>("ImageCategories")
                        .IsRequired()
                        .HasColumnType("integer[]");

                    b.Property<int[]>("ImageSizes")
                        .IsRequired()
                        .HasColumnType("integer[]");

                    b.Property<int[]>("ImageTypes")
                        .IsRequired()
                        .HasColumnType("integer[]");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int?>("ProspectId")
                        .HasColumnType("integer");

                    b.Property<int?>("StandardType")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<int?>("UpdateStatus")
                        .HasColumnType("integer");

                    b.Property<string>("Url")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ProspectId");

                    b.ToTable("AbpExportTemplates");
                });

            modelBuilder.Entity("aibase.FileEntity.File", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Height")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Size")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Width")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ImageId");

                    b.ToTable("AbpFiles");
                });

            modelBuilder.Entity("aibase.GeologyDatas.GeologyData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("AttributeName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("AttributeValue")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("DrillHole")
                        .HasColumnType("text");

                    b.Property<int>("GeologySuiteId")
                        .HasColumnType("integer");

                    b.Property<string>("GroupId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int?>("RockTypeId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RockTypeId");

                    b.ToTable("AbpGeologyData");
                });

            modelBuilder.Entity("aibase.GeologyDateValues.GeologyDateValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("DateValue")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteFieldId");

                    b.ToTable("AbpGeologyDateValues");
                });

            modelBuilder.Entity("aibase.GeologyDates.GeologyDate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpGeologyDates");
                });

            modelBuilder.Entity("aibase.GeologyDescriptionValues.GeologyDescriptionValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteFieldId");

                    b.ToTable("AbpGeologyDescriptionValues");
                });

            modelBuilder.Entity("aibase.GeologyDescriptions.GeologyDescription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FieldHeight")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpGeologyDescriptions");
                });

            modelBuilder.Entity("aibase.GeologyFields.GeologyField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColour")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("GeologyDateId")
                        .HasColumnType("integer");

                    b.Property<int?>("GeologyDescriptionId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMandatory")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("NumberId")
                        .HasColumnType("integer");

                    b.Property<int?>("PickListId")
                        .HasColumnType("integer");

                    b.Property<int?>("RockGroupId")
                        .HasColumnType("integer");

                    b.Property<int?>("RockNodeId")
                        .HasColumnType("integer");

                    b.Property<int?>("RockSelectNumberId")
                        .HasColumnType("integer");

                    b.Property<int?>("RockTypeNumberId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColour")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeologyDateId");

                    b.HasIndex("GeologyDescriptionId");

                    b.HasIndex("NumberId");

                    b.HasIndex("PickListId");

                    b.HasIndex("RockGroupId");

                    b.HasIndex("RockNodeId");

                    b.HasIndex("RockSelectNumberId");

                    b.HasIndex("RockTypeNumberId");

                    b.ToTable("AbpGeologyFields");
                });

            modelBuilder.Entity("aibase.GeologyProjectSuites.GeologyProjectSuite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("GeologySuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteId");

                    b.HasIndex("ProjectId");

                    b.ToTable("AbpGeologyProjectSuites");
                });

            modelBuilder.Entity("aibase.GeologySuiteFields.GeologySuiteField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColour")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("GeologyFieldId")
                        .HasColumnType("integer");

                    b.Property<int>("GeologySuiteId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsMandatory")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Sequence")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColour")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("GeologyFieldId");

                    b.HasIndex("GeologySuiteId");

                    b.ToTable("AbpGeologySuiteFields");
                });

            modelBuilder.Entity("aibase.GeologySuites.GeologySuite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGeotech")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpGeologySuites");
                });

            modelBuilder.Entity("aibase.GeotechDatas.GeotechData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<double?>("AlphaAngle")
                        .HasColumnType("double precision");

                    b.Property<double?>("BetaAngle")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("Depth")
                        .HasColumnType("double precision");

                    b.Property<double?>("DepthTo")
                        .HasColumnType("double precision");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<int>("GeotechSuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("ImageCropId")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageCropIdTo")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("RockGroupId")
                        .HasColumnType("integer");

                    b.Property<int?>("RockTypeId")
                        .HasColumnType("integer");

                    b.Property<int?>("StructureConditionId")
                        .HasColumnType("integer");

                    b.Property<int>("StructureId")
                        .HasColumnType("integer");

                    b.Property<double?>("Width")
                        .HasColumnType("double precision");

                    b.Property<double>("X")
                        .HasColumnType("double precision");

                    b.Property<double?>("XTo")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("GeotechSuiteId");

                    b.HasIndex("RockGroupId");

                    b.HasIndex("RockTypeId");

                    b.HasIndex("StructureConditionId");

                    b.HasIndex("StructureId");

                    b.ToTable("AbpGeotechData");
                });

            modelBuilder.Entity("aibase.GeotechSuiteStructures.GeotechSuiteStructure", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("GeotechSuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("StructureId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeotechSuiteId");

                    b.HasIndex("StructureId");

                    b.ToTable("AbpGeotechSuiteStructures");
                });

            modelBuilder.Entity("aibase.GeotechSuites.GeotechSuite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpGeotechSuites");
                });

            modelBuilder.Entity("aibase.ImageCrops.ImageCrop", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Coordinate")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("DepthFrom")
                        .HasColumnType("double precision");

                    b.Property<double>("DepthTo")
                        .HasColumnType("double precision");

                    b.Property<int>("ImageId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MediumSize")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SegmentResutl")
                        .HasColumnType("text");

                    b.Property<string>("Type")
                        .HasColumnType("text");

                    b.Property<string>("UrlCroppedImage")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("DepthFrom");

                    b.HasIndex("ImageId");

                    b.HasIndex("ImageId", "Type");

                    b.ToTable("AbpImageCrops");
                });

            modelBuilder.Entity("aibase.ImageEntity.Image", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BoundingBox")
                        .HasColumnType("text");

                    b.Property<string>("BoundingRows")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByName")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByUser")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("DepthFrom")
                        .HasColumnType("double precision");

                    b.Property<double?>("DepthFromOcr")
                        .HasColumnType("double precision");

                    b.Property<double>("DepthTo")
                        .HasColumnType("double precision");

                    b.Property<double?>("DepthToOcr")
                        .HasColumnType("double precision");

                    b.Property<string>("DirectOcrResult")
                        .HasColumnType("text");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<string>("DrillholeNameOcr")
                        .HasColumnType("text");

                    b.Property<string>("Fractures")
                        .HasColumnType("text");

                    b.Property<int>("ImageCategory")
                        .HasColumnType("integer");

                    b.Property<int>("ImageClass")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageStatus")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageSubtypeId")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageTypeId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OcrResult")
                        .HasColumnType("text");

                    b.Property<string>("OriginalBoundingBox")
                        .HasColumnType("text");

                    b.Property<string>("OriginalBoundingRows")
                        .HasColumnType("text");

                    b.Property<string>("OriginalDirectOcrResult")
                        .HasColumnType("text");

                    b.Property<string>("OriginalDrillholeNameOcr")
                        .HasColumnType("text");

                    b.Property<string>("OriginalFractures")
                        .HasColumnType("text");

                    b.Property<string>("OriginalSegmentResult")
                        .HasColumnType("text");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("ProspectId")
                        .HasColumnType("integer");

                    b.Property<string>("SegmentDetailResult")
                        .HasColumnType("text");

                    b.Property<string>("SegmentResult")
                        .HasColumnType("text");

                    b.Property<int?>("StandardType")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ImageSubtypeId");

                    b.HasIndex("ImageTypeId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ProspectId");

                    b.HasIndex("DrillHoleId", "ImageClass", "Type");

                    b.ToTable("AbpImages");
                });

            modelBuilder.Entity("aibase.ImageOcrs.ImageOcr", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ImageId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ModelId")
                        .HasColumnType("integer");

                    b.Property<double>("height")
                        .HasColumnType("double precision");

                    b.Property<double>("probability")
                        .HasColumnType("double precision");

                    b.Property<string>("text")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("type")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double>("width")
                        .HasColumnType("double precision");

                    b.Property<double>("x")
                        .HasColumnType("double precision");

                    b.Property<double>("y")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.ToTable("AbpImageOcrs");
                });

            modelBuilder.Entity("aibase.ImageSegments.ImageSegment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ImageId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Result")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AbpImageSegments");
                });

            modelBuilder.Entity("aibase.ImageSubtypes.ImageSubtype", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ImageTypeId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDry")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUv")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsWet")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Sequence")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ImageTypeId");

                    b.ToTable("AbpImageSubtypes");
                });

            modelBuilder.Entity("aibase.ImageTypes.ImageType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRig")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRigCorrected")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsStandard")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Priority")
                        .HasColumnType("integer");

                    b.Property<int>("Sequence")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpImageTypes");
                });

            modelBuilder.Entity("aibase.ImportMappingTemplates.ImportMappingTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ImportFileType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("SuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpImportMappingTemplates");
                });

            modelBuilder.Entity("aibase.ImportMappingTemplates.ImportMappingTemplateField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileColumnName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ImportMappingTemplateId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Sequence")
                        .HasColumnType("integer");

                    b.Property<string>("SystemFieldName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ImportMappingTemplateId");

                    b.ToTable("AbpImportMappingTemplateFields");
                });

            modelBuilder.Entity("aibase.LoggingBars.LoggingBar", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<List<int>>("BetweenImageCropIds")
                        .IsRequired()
                        .HasColumnType("integer[]");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<double>("DepthFrom")
                        .HasColumnType("double precision");

                    b.Property<double>("DepthTo")
                        .HasColumnType("double precision");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<int>("EndImageCropId")
                        .HasColumnType("integer");

                    b.Property<double>("EndX")
                        .HasColumnType("double precision");

                    b.Property<int>("GeologySuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("ImageSubtypeId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("StartImageCropId")
                        .HasColumnType("integer");

                    b.Property<double>("StartX")
                        .HasColumnType("double precision");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpLoggingBars");
                });

            modelBuilder.Entity("aibase.LoggingViewColumns.LoggingViewColumn", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("AssayAttributeId")
                        .HasColumnType("integer");

                    b.Property<int?>("AssaySuiteId")
                        .HasColumnType("integer");

                    b.Property<int?>("AttributeId")
                        .HasColumnType("integer");

                    b.Property<int>("ColumnClass")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<int?>("GeologySuiteId")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageSubtypeId")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageTypeId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("LoggingViewId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("NumberRangeId")
                        .HasColumnType("integer");

                    b.Property<int>("Sequence")
                        .HasColumnType("integer");

                    b.Property<int?>("StandardType")
                        .HasColumnType("integer");

                    b.Property<int?>("SuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<int?>("Type")
                        .HasColumnType("integer");

                    b.Property<int>("Width")
                        .HasColumnType("integer");

                    b.Property<double?>("WidthFactor")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("AssayAttributeId");

                    b.HasIndex("AssaySuiteId");

                    b.HasIndex("AttributeId");

                    b.HasIndex("GeologySuiteFieldId");

                    b.HasIndex("GeologySuiteId");

                    b.HasIndex("ImageSubtypeId");

                    b.HasIndex("ImageTypeId");

                    b.HasIndex("LoggingViewId");

                    b.HasIndex("NumberRangeId");

                    b.HasIndex("SuiteId");

                    b.ToTable("AbpLoggingViewColumns");
                });

            modelBuilder.Entity("aibase.LoggingViews.LoggingView", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Sequence")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpLoggingViews");
                });

            modelBuilder.Entity("aibase.MobileProfiles.MobileProfile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double?>("DepthIncrement")
                        .HasColumnType("double precision");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int>("ExternalCameraType")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsApplyDepthIncrement")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsDepthIncrement")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDry")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRig")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsStandard")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsUv")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsWet")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MobileCameraType")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("RotateImgExternal")
                        .HasColumnType("integer");

                    b.Property<int?>("RotateImgMobile")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpMobileProfiles");
                });

            modelBuilder.Entity("aibase.MultiTenancy.Tenant", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AccountType")
                        .HasColumnType("integer");

                    b.Property<string>("ConnectionString")
                        .HasMaxLength(1024)
                        .HasColumnType("character varying(1024)");

                    b.Property<string>("Country")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeactivationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("DeleterUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("EditionId")
                        .HasColumnType("integer");

                    b.Property<string>("FirstAddress")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("character varying(128)");

                    b.Property<string>("PostCode")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("SecondAddress")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("State")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Suburb")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("TenancyName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.HasKey("Id");

                    b.HasIndex("CreatorUserId");

                    b.HasIndex("DeleterUserId");

                    b.HasIndex("EditionId");

                    b.HasIndex("LastModifierUserId");

                    b.HasIndex("TenancyName");

                    b.ToTable("AbpTenants");
                });

            modelBuilder.Entity("aibase.NumberRanges.NumberRange", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("MaxDisplayValue")
                        .HasColumnType("double precision");

                    b.Property<double>("MinDisplayValue")
                        .HasColumnType("double precision");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpNumberRanges");
                });

            modelBuilder.Entity("aibase.NumberRanges.NumberRangeInterval", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("FontSize")
                        .HasColumnType("double precision");

                    b.Property<double>("IntervalMax")
                        .HasColumnType("double precision");

                    b.Property<double>("IntervalMin")
                        .HasColumnType("double precision");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsBold")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("NumberRangeId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("NumberRangeId");

                    b.ToTable("AbpNumberRangeIntervals");
                });

            modelBuilder.Entity("aibase.NumberValues.NumberValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<int>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double?>("Value")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteFieldId");

                    b.ToTable("AbpNumberValues");
                });

            modelBuilder.Entity("aibase.Numbers.Number", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPercent")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("LowerLimit")
                        .HasColumnType("double precision");

                    b.Property<double?>("LowerMajor")
                        .HasColumnType("double precision");

                    b.Property<double?>("LowerMinor")
                        .HasColumnType("double precision");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<int>("UnitId")
                        .HasColumnType("integer");

                    b.Property<double>("UpperLimit")
                        .HasColumnType("double precision");

                    b.Property<double?>("UpperMajor")
                        .HasColumnType("double precision");

                    b.Property<double?>("UpperMinor")
                        .HasColumnType("double precision");

                    b.Property<int>("ValueType")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("UnitId");

                    b.ToTable("AbpNumbers");
                });

            modelBuilder.Entity("aibase.PickListItems.PickListItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("PickListId")
                        .HasColumnType("integer");

                    b.Property<int>("Sequence")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PickListId");

                    b.ToTable("AbpPickListItems");
                });

            modelBuilder.Entity("aibase.PickListValues.PickListValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<int>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("PickListItemId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteFieldId");

                    b.HasIndex("PickListItemId");

                    b.ToTable("AbpPickListValues");
                });

            modelBuilder.Entity("aibase.PickLists.PickList", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpPickLists");
                });

            modelBuilder.Entity("aibase.Polygons.Polygon", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Coordinates")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpPolygons");
                });

            modelBuilder.Entity("aibase.ProjectEntity.Project", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BoundingBoxIds")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("BoundingRowsIds")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<double>("CoreTrayLength")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LoggingTextColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("MobileProfileId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("RockGroupId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("WorkflowId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("MobileProfileId");

                    b.HasIndex("RockGroupId");

                    b.HasIndex("WorkflowId");

                    b.ToTable("AbpProjects");
                });

            modelBuilder.Entity("aibase.ProjectGeotechSuites.ProjectGeotechSuite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("GeotechSuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeotechSuiteId");

                    b.HasIndex("ProjectId");

                    b.ToTable("AbpProjectGeotechSuites");
                });

            modelBuilder.Entity("aibase.ProjectImageTypes.ProjectImageType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ImageTypeId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ImageTypeId");

                    b.HasIndex("ProjectId");

                    b.ToTable("AbpProjectImageTypes");
                });

            modelBuilder.Entity("aibase.ProjectLoggingViews.ProjectLoggingView", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("LoggingViewId")
                        .HasColumnType("integer");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("LoggingViewId");

                    b.HasIndex("ProjectId");

                    b.ToTable("AbpProjectLoggingViews");
                });

            modelBuilder.Entity("aibase.ProjectSuites.ProjectSuite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("SuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.HasIndex("SuiteId");

                    b.ToTable("AbpProjectSuites");
                });

            modelBuilder.Entity("aibase.Prospects.Prospect", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.ToTable("AbpProspects");
                });

            modelBuilder.Entity("aibase.RecoveryResults.RecoveryResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("DepthInterval")
                        .HasColumnType("double precision");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<int>("FromImageCropId")
                        .HasColumnType("integer");

                    b.Property<string>("FromOcrId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("FromRowIndex")
                        .HasColumnType("integer");

                    b.Property<double>("FromX")
                        .HasColumnType("double precision");

                    b.Property<double>("FromY")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("Length")
                        .HasColumnType("double precision");

                    b.Property<double>("OcrValueFrom")
                        .HasColumnType("double precision");

                    b.Property<double>("OcrValueTo")
                        .HasColumnType("double precision");

                    b.Property<double>("Recovery")
                        .HasColumnType("double precision");

                    b.Property<int>("ToImageCropId")
                        .HasColumnType("integer");

                    b.Property<string>("ToOcrId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ToRowIndex")
                        .HasColumnType("integer");

                    b.Property<double>("ToX")
                        .HasColumnType("double precision");

                    b.Property<double>("ToY")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("DrillHoleId");

                    b.ToTable("AbpRecoveryResults");
                });

            modelBuilder.Entity("aibase.ResultSteps.ResultStep", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ImageId")
                        .HasColumnType("integer");

                    b.Property<string>("Input")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Output")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("StepId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpResultSteps");
                });

            modelBuilder.Entity("aibase.RockGroupProjects.RockGroupProject", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("RockGroupId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.HasIndex("RockGroupId");

                    b.ToTable("AbpRockGroupProjects");
                });

            modelBuilder.Entity("aibase.RockGroupRockTypes.RockGroupRockType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("RockGroupId")
                        .HasColumnType("integer");

                    b.Property<int>("RockTypeId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RockGroupId");

                    b.HasIndex("RockTypeId");

                    b.ToTable("AbpRockGroupRockTypes");
                });

            modelBuilder.Entity("aibase.RockGroupValues.RockGroupValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<int>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("RockTypeId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteFieldId");

                    b.HasIndex("RockTypeId");

                    b.ToTable("AbpRockGroupValues");
                });

            modelBuilder.Entity("aibase.RockGroups.RockGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpRockGroups");
                });

            modelBuilder.Entity("aibase.RockLines.RockLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("DepthFrom")
                        .HasColumnType("double precision");

                    b.Property<double>("DepthTo")
                        .HasColumnType("double precision");

                    b.Property<double>("EndX")
                        .HasColumnType("double precision");

                    b.Property<double>("EndY")
                        .HasColumnType("double precision");

                    b.Property<int>("ImageCropId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("Length")
                        .HasColumnType("double precision");

                    b.Property<int>("RowIndex")
                        .HasColumnType("integer");

                    b.Property<double>("StartX")
                        .HasColumnType("double precision");

                    b.Property<double>("StartY")
                        .HasColumnType("double precision");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ImageCropId");

                    b.ToTable("AbpRockLines");
                });

            modelBuilder.Entity("aibase.RockNodeValues.RockNodeValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<int>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("RockNodeId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteFieldId");

                    b.HasIndex("RockNodeId");

                    b.ToTable("AbpRockNodeValues");
                });

            modelBuilder.Entity("aibase.RockSelectNumberValues.RockSelectNumberValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<int>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double?>("NumberValue")
                        .HasColumnType("double precision");

                    b.Property<int?>("RockTypeId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteFieldId");

                    b.HasIndex("RockTypeId");

                    b.ToTable("AbpRockSelectNumberValues");
                });

            modelBuilder.Entity("aibase.RockSelectNumbers.RockSelectNumber", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NumberId")
                        .HasColumnType("integer");

                    b.Property<int>("RockGroupId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("NumberId");

                    b.HasIndex("RockGroupId");

                    b.ToTable("AbpRockSelectNumbers");
                });

            modelBuilder.Entity("aibase.RockStyles.RockStyle", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FillColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FillTexture")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("FillTransparency")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LineColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("LineStyle")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<double>("LineThickness")
                        .HasColumnType("double precision");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpRockStyles");
                });

            modelBuilder.Entity("aibase.RockTree.RockNode", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DeleterUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("DisplayColor")
                        .HasColumnType("text");

                    b.Property<string>("IconUrl")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NodeType")
                        .HasColumnType("integer");

                    b.Property<int?>("RockTypeId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RockTypeId");

                    b.ToTable("RockNodes", (string)null);
                });

            modelBuilder.Entity("aibase.RockTree.RockNodeRelation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ChildNodeId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("boolean");

                    b.Property<int>("ParentNodeId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ChildNodeId");

                    b.HasIndex("ParentNodeId");

                    b.ToTable("RockNodeRelations", (string)null);
                });

            modelBuilder.Entity("aibase.RockTree.RockTreeRoot", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("CreatorUserId")
                        .HasColumnType("bigint");

                    b.Property<long?>("DeleterUserId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<int?>("GeologySuiteId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("LastModifierUserId")
                        .HasColumnType("bigint");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteId")
                        .IsUnique();

                    b.ToTable("RockTreeRoots", (string)null);
                });

            modelBuilder.Entity("aibase.RockTree.RockTreeRootNode", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<int>("RockNodeId")
                        .HasColumnType("integer");

                    b.Property<int>("RockTreeRootId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RockNodeId");

                    b.HasIndex("RockTreeRootId");

                    b.ToTable("RockTreeRootNodes", (string)null);
                });

            modelBuilder.Entity("aibase.RockTypeNumberValues.RockTypeNumberValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataEntryId")
                        .HasColumnType("integer");

                    b.Property<int>("GeologySuiteFieldId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double?>("NumberValue")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteFieldId");

                    b.ToTable("AbpRockTypeNumberValues");
                });

            modelBuilder.Entity("aibase.RockTypeNumbers.RockTypeNumber", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("NumberId")
                        .HasColumnType("integer");

                    b.Property<int>("RockTypeId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("NumberId");

                    b.HasIndex("RockTypeId");

                    b.ToTable("AbpRockTypeNumbers");
                });

            modelBuilder.Entity("aibase.RockTypes.RockType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("RockStyleId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RockStyleId");

                    b.ToTable("AbpRockTypes");
                });

            modelBuilder.Entity("aibase.RqdCalculations.RqdCalculation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CalculationType")
                        .HasColumnType("integer");

                    b.Property<int>("CountingMethod")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double?>("Interval")
                        .HasColumnType("double precision");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsDisplay")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("MinimumSegmentLength")
                        .HasColumnType("integer");

                    b.Property<decimal?>("MinimumWidth")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("StructureId")
                        .HasColumnType("integer");

                    b.Property<int?>("StructureTypeId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("StructureId");

                    b.HasIndex("StructureTypeId");

                    b.ToTable("AbpRqdCalculations");
                });

            modelBuilder.Entity("aibase.RqdCalculations.RqdCalculationProject", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("RqdCalculationId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.HasIndex("RqdCalculationId");

                    b.ToTable("AbpRqdCalculationProjects");
                });

            modelBuilder.Entity("aibase.RqdCalculations.RqdCalculationResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<double>("FromDepth")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("RqdCalculationId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<double>("ToDepth")
                        .HasColumnType("double precision");

                    b.Property<double>("Total")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("DrillHoleId");

                    b.HasIndex("RqdCalculationId");

                    b.ToTable("AbpRqdCalculationResults");
                });

            modelBuilder.Entity("aibase.RqdPercentResults.RqdPercentResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("DepthInterval")
                        .HasColumnType("double precision");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<int>("FromImageCropId")
                        .HasColumnType("integer");

                    b.Property<string>("FromOcrId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("FromRowIndex")
                        .HasColumnType("integer");

                    b.Property<double>("FromX")
                        .HasColumnType("double precision");

                    b.Property<double>("FromY")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("Length")
                        .HasColumnType("double precision");

                    b.Property<int>("NumberOfPieces")
                        .HasColumnType("integer");

                    b.Property<double>("OcrValueFrom")
                        .HasColumnType("double precision");

                    b.Property<double>("OcrValueTo")
                        .HasColumnType("double precision");

                    b.Property<int>("RqdCalculationId")
                        .HasColumnType("integer");

                    b.Property<int>("ToImageCropId")
                        .HasColumnType("integer");

                    b.Property<string>("ToOcrId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ToRowIndex")
                        .HasColumnType("integer");

                    b.Property<double>("ToX")
                        .HasColumnType("double precision");

                    b.Property<double>("ToY")
                        .HasColumnType("double precision");

                    b.Property<double>("Total")
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("DrillHoleId");

                    b.HasIndex("RqdCalculationId");

                    b.ToTable("AbpRqdPercentResults");
                });

            modelBuilder.Entity("aibase.Settings.Setting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("BoundingPolygonType")
                        .HasColumnType("integer");

                    b.Property<string>("CollectionName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CollectionNameSingular")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DrillHoleView")
                        .HasColumnType("integer");

                    b.Property<List<int>>("DrillholeIds")
                        .HasColumnType("integer[]");

                    b.Property<int?>("ImageStatus")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LogoProduct")
                        .HasColumnType("text");

                    b.Property<double>("OverlayOpacity")
                        .HasColumnType("double precision");

                    b.Property<string>("ProductName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<List<int>>("ProjectIds")
                        .HasColumnType("integer[]");

                    b.Property<List<int>>("ProspectIds")
                        .HasColumnType("integer[]");

                    b.Property<bool>("RowPolygon")
                        .HasColumnType("boolean");

                    b.Property<int?>("RowPolygonType")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("Units")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UnitsSymbol")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("ViewWithBoundingPolygon")
                        .HasColumnType("boolean");

                    b.Property<bool?>("isUseLogo")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.ToTable("AbpSettingAccounts");
                });

            modelBuilder.Entity("aibase.SourceTypeWorkflowEntity.SourceTypeWorkflow", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpSourceTypeWorkflow");
                });

            modelBuilder.Entity("aibase.StepWorkflowEntity.StepWorkflow", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int?>("BoundingBoxId")
                        .HasColumnType("integer");

                    b.Property<int?>("BoundingRowOption")
                        .HasColumnType("integer");

                    b.Property<int?>("BoundingRowsId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DataSourceType")
                        .HasColumnType("integer");

                    b.Property<string>("DataValue")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<List<int>>("ImageSubtypesAdditional")
                        .HasColumnType("integer[]");

                    b.Property<List<int>>("ImageTypesAdditional")
                        .HasColumnType("integer[]");

                    b.Property<bool>("IsCropAdditional")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("ModelId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("OutputType")
                        .HasColumnType("integer");

                    b.Property<int?>("PolygonId")
                        .HasColumnType("integer");

                    b.Property<int>("ProcessType")
                        .HasColumnType("integer");

                    b.Property<string>("Prompt")
                        .HasColumnType("text");

                    b.Property<bool>("SegmentFlag")
                        .HasColumnType("boolean");

                    b.Property<int?>("ToolType")
                        .HasColumnType("integer");

                    b.Property<int>("WorkflowId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("BoundingBoxId");

                    b.HasIndex("BoundingRowsId");

                    b.ToTable("AbpStepWorkflows");
                });

            modelBuilder.Entity("aibase.StructureConditions.StructureCondition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpStructureConditions");
                });

            modelBuilder.Entity("aibase.StructureTypes.StructureType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpStructureTypes");
                });

            modelBuilder.Entity("aibase.Structures.Structure", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("HasCondition")
                        .HasColumnType("boolean");

                    b.Property<bool>("HasMineral")
                        .HasColumnType("boolean");

                    b.Property<bool>("HasOrientation")
                        .HasColumnType("boolean");

                    b.Property<bool>("HasWidth")
                        .HasColumnType("boolean");

                    b.Property<string>("Icon")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("RockGroupId")
                        .HasColumnType("integer");

                    b.Property<int>("Selector")
                        .HasColumnType("integer");

                    b.Property<int>("StructureTypeId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<string>("TextColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RockGroupId");

                    b.HasIndex("StructureTypeId");

                    b.ToTable("AbpStructures");
                });

            modelBuilder.Entity("aibase.SuiteAttributes.SuiteAttribute", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("AttributeId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AttributeId");

                    b.HasIndex("SuiteId");

                    b.ToTable("AbpSuiteAttributes");
                });

            modelBuilder.Entity("aibase.Suites.Suite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpSuites");
                });

            modelBuilder.Entity("aibase.Themes.Theme", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("CicleColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FillLine")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LineStroke")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("StrokeWidth")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<double>("Transparency")
                        .HasColumnType("double precision");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpThemes");
                });

            modelBuilder.Entity("aibase.TrayDepthResults.TrayDepthResult", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DrillHoleId")
                        .HasColumnType("integer");

                    b.Property<double>("EndDepth")
                        .HasColumnType("double precision");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<double>("StartDepth")
                        .HasColumnType("double precision");

                    b.Property<int>("TrayNumber")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DrillHoleId");

                    b.ToTable("AbpTrayDepthResults");
                });

            modelBuilder.Entity("aibase.Units.Unit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("character varying(4)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpUnits");
                });

            modelBuilder.Entity("aibase.UserProjects.UserProject", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<long>("userId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("ProjectId");

                    b.HasIndex("userId");

                    b.ToTable("AbpUserProjects");
                });

            modelBuilder.Entity("aibase.UserRoleConfigs.UserRoleConfig", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int[]>("Functions")
                        .IsRequired()
                        .HasColumnType("integer[]");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpUserRoleConfigs");
                });

            modelBuilder.Entity("aibase.WorkRoleGeotechSuites.WorkRoleGeotechSuite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("GeotechSuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("WorkRoleId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeotechSuiteId");

                    b.HasIndex("WorkRoleId");

                    b.ToTable("AbpWorkRoleGeotechSuites");
                });

            modelBuilder.Entity("aibase.WorkRoleSuites.WorkRoleSuite", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("GeologySuiteId")
                        .HasColumnType("integer");

                    b.Property<int>("WorkRoleId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("GeologySuiteId");

                    b.HasIndex("WorkRoleId");

                    b.ToTable("AbpWorkRoleSuites");
                });

            modelBuilder.Entity("aibase.WorkRoles.WorkRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpWorkRoles");
                });

            modelBuilder.Entity("aibase.WorkflowEntity.Workflow", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("BackgroundColor")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ButtonName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("AbpWorkflows");
                });

            modelBuilder.Entity("aibase.WorkflowJobs.WorkflowJob", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("DrillholeId")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageCategory")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageClass")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageSubtypeId")
                        .HasColumnType("integer");

                    b.Property<int?>("ImageTypeId")
                        .HasColumnType("integer");

                    b.Property<string>("JobId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("ProspectId")
                        .HasColumnType("integer");

                    b.Property<int?>("StandardType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int?>("StatusDone")
                        .HasColumnType("integer");

                    b.Property<int?>("StatusFilter")
                        .HasColumnType("integer");

                    b.Property<int>("TenantId")
                        .HasColumnType("integer");

                    b.Property<int?>("TotalCompleted")
                        .HasColumnType("integer");

                    b.Property<int?>("TotalErrors")
                        .HasColumnType("integer");

                    b.Property<int?>("TotalImage")
                        .HasColumnType("integer");

                    b.Property<int?>("Type")
                        .HasColumnType("integer");

                    b.Property<int>("WorkflowId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("DrillholeId");

                    b.HasIndex("ImageSubtypeId");

                    b.HasIndex("ImageTypeId");

                    b.HasIndex("ProjectId");

                    b.HasIndex("ProspectId");

                    b.HasIndex("WorkflowId");

                    b.ToTable("AbpWorkflowJobs");
                });

            modelBuilder.Entity("aibase.WorkflowJobs.WorkflowJobError", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ImageId")
                        .HasColumnType("integer");

                    b.Property<string>("StackTrace")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("TenantId")
                        .HasColumnType("integer");

                    b.Property<long?>("UserId")
                        .HasColumnType("bigint");

                    b.Property<int>("WorkflowJobId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ImageId");

                    b.HasIndex("WorkflowJobId");

                    b.ToTable("AbpWorkflowJobErrors");
                });

            modelBuilder.Entity("Abp.Application.Features.EditionFeatureSetting", b =>
                {
                    b.HasBaseType("Abp.Application.Features.FeatureSetting");

                    b.Property<int>("EditionId")
                        .HasColumnType("integer");

                    b.HasIndex("EditionId", "Name");

                    b.ToTable("AbpFeatures");

                    b.HasDiscriminator().HasValue("EditionFeatureSetting");
                });

            modelBuilder.Entity("Abp.MultiTenancy.TenantFeatureSetting", b =>
                {
                    b.HasBaseType("Abp.Application.Features.FeatureSetting");

                    b.HasIndex("TenantId", "Name");

                    b.ToTable("AbpFeatures");

                    b.HasDiscriminator().HasValue("TenantFeatureSetting");
                });

            modelBuilder.Entity("Abp.Authorization.Roles.RolePermissionSetting", b =>
                {
                    b.HasBaseType("Abp.Authorization.PermissionSetting");

                    b.Property<int>("RoleId")
                        .HasColumnType("integer");

                    b.HasIndex("RoleId");

                    b.ToTable("AbpPermissions");

                    b.HasDiscriminator().HasValue("RolePermissionSetting");
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserPermissionSetting", b =>
                {
                    b.HasBaseType("Abp.Authorization.PermissionSetting");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasIndex("UserId");

                    b.ToTable("AbpPermissions");

                    b.HasDiscriminator().HasValue("UserPermissionSetting");
                });

            modelBuilder.Entity("Abp.Authorization.Roles.RoleClaim", b =>
                {
                    b.HasOne("aibase.Authorization.Roles.Role", null)
                        .WithMany("Claims")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserClaim", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", null)
                        .WithMany("Claims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserLogin", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", null)
                        .WithMany("Logins")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserRole", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", null)
                        .WithMany("Roles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserToken", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", null)
                        .WithMany("Tokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Abp.Configuration.Setting", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", null)
                        .WithMany("Settings")
                        .HasForeignKey("UserId");
                });

            modelBuilder.Entity("Abp.DynamicEntityProperties.DynamicEntityProperty", b =>
                {
                    b.HasOne("Abp.DynamicEntityProperties.DynamicProperty", "DynamicProperty")
                        .WithMany()
                        .HasForeignKey("DynamicPropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DynamicProperty");
                });

            modelBuilder.Entity("Abp.DynamicEntityProperties.DynamicEntityPropertyValue", b =>
                {
                    b.HasOne("Abp.DynamicEntityProperties.DynamicEntityProperty", "DynamicEntityProperty")
                        .WithMany()
                        .HasForeignKey("DynamicEntityPropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DynamicEntityProperty");
                });

            modelBuilder.Entity("Abp.DynamicEntityProperties.DynamicPropertyValue", b =>
                {
                    b.HasOne("Abp.DynamicEntityProperties.DynamicProperty", "DynamicProperty")
                        .WithMany("DynamicPropertyValues")
                        .HasForeignKey("DynamicPropertyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DynamicProperty");
                });

            modelBuilder.Entity("Abp.EntityHistory.EntityChange", b =>
                {
                    b.HasOne("Abp.EntityHistory.EntityChangeSet", null)
                        .WithMany("EntityChanges")
                        .HasForeignKey("EntityChangeSetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Abp.EntityHistory.EntityPropertyChange", b =>
                {
                    b.HasOne("Abp.EntityHistory.EntityChange", null)
                        .WithMany("PropertyChanges")
                        .HasForeignKey("EntityChangeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Abp.Organizations.OrganizationUnit", b =>
                {
                    b.HasOne("Abp.Organizations.OrganizationUnit", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("Abp.Webhooks.WebhookSendAttempt", b =>
                {
                    b.HasOne("Abp.Webhooks.WebhookEvent", "WebhookEvent")
                        .WithMany()
                        .HasForeignKey("WebhookEventId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("WebhookEvent");
                });

            modelBuilder.Entity("aibase.APIKeys.APIKey", b =>
                {
                    b.HasOne("aibase.MultiTenancy.Tenant", "Tenant")
                        .WithMany()
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tenant");
                });

            modelBuilder.Entity("aibase.APIKeys.APIKeyRole", b =>
                {
                    b.HasOne("aibase.APIKeys.APIKey", "ApiKey")
                        .WithMany("APIKeyRoles")
                        .HasForeignKey("ApiKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.APIKeys.APIKeyTypes", "ApiKeyType")
                        .WithMany("APIKeyRoles")
                        .HasForeignKey("ApiKeyTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");

                    b.Navigation("ApiKeyType");
                });

            modelBuilder.Entity("aibase.AssayDatas.AssayData", b =>
                {
                    b.HasOne("aibase.AssaySuites.AssaySuite", "AssaySuite")
                        .WithMany()
                        .HasForeignKey("AssaySuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssaySuite");
                });

            modelBuilder.Entity("aibase.AssayProjectSuites.AssayProjectSuite", b =>
                {
                    b.HasOne("aibase.AssaySuites.AssaySuite", "AssaySuite")
                        .WithMany("AssayProjectSuites")
                        .HasForeignKey("AssaySuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany("AssayProjectSuites")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssaySuite");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("aibase.AssaySuiteAttributes.AssaySuiteAttribute", b =>
                {
                    b.HasOne("aibase.AssayAttributes.AssayAttribute", "AssayAttribute")
                        .WithMany("AssaySuiteAttributes")
                        .HasForeignKey("AssayAttributeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.AssaySuites.AssaySuite", "AssaySuite")
                        .WithMany("AssaySuiteAttributes")
                        .HasForeignKey("AssaySuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssayAttribute");

                    b.Navigation("AssaySuite");
                });

            modelBuilder.Entity("aibase.AssaySuiteFields.AssaySuiteField", b =>
                {
                    b.HasOne("aibase.AssayAttributes.AssayAttribute", "AssayAttribute")
                        .WithMany()
                        .HasForeignKey("AssayAttributeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.AssaySuites.AssaySuite", "AssaySuite")
                        .WithMany("AssaySuiteFields")
                        .HasForeignKey("AssaySuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssayAttribute");

                    b.Navigation("AssaySuite");
                });

            modelBuilder.Entity("aibase.Authorization.Roles.Role", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", "CreatorUser")
                        .WithMany()
                        .HasForeignKey("CreatorUserId");

                    b.HasOne("aibase.Authorization.Users.User", "DeleterUser")
                        .WithMany()
                        .HasForeignKey("DeleterUserId");

                    b.HasOne("aibase.Authorization.Users.User", "LastModifierUser")
                        .WithMany()
                        .HasForeignKey("LastModifierUserId");

                    b.Navigation("CreatorUser");

                    b.Navigation("DeleterUser");

                    b.Navigation("LastModifierUser");
                });

            modelBuilder.Entity("aibase.Authorization.Users.RefreshToken", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("aibase.Authorization.Users.User", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", "CreatorUser")
                        .WithMany()
                        .HasForeignKey("CreatorUserId");

                    b.HasOne("aibase.Authorization.Users.User", "DeleterUser")
                        .WithMany()
                        .HasForeignKey("DeleterUserId");

                    b.HasOne("aibase.Authorization.Users.User", "LastModifierUser")
                        .WithMany()
                        .HasForeignKey("LastModifierUserId");

                    b.HasOne("aibase.UserRoleConfigs.UserRoleConfig", "UserRoleConfig")
                        .WithMany()
                        .HasForeignKey("UserRoleConfigId");

                    b.HasOne("aibase.WorkRoles.WorkRole", "WorkRole")
                        .WithMany()
                        .HasForeignKey("WorkRoleId");

                    b.Navigation("CreatorUser");

                    b.Navigation("DeleterUser");

                    b.Navigation("LastModifierUser");

                    b.Navigation("UserRoleConfig");

                    b.Navigation("WorkRole");
                });

            modelBuilder.Entity("aibase.ColourValues.ColourValue", b =>
                {
                    b.HasOne("aibase.Colours.Colour", "Colour")
                        .WithMany()
                        .HasForeignKey("ColourId");

                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Colour");

                    b.Navigation("GeologySuiteField");
                });

            modelBuilder.Entity("aibase.CoordinatePolygons.CoordinatePolygon", b =>
                {
                    b.HasOne("aibase.Polygons.Polygon", "Polygon")
                        .WithMany("CoordinatePolygons")
                        .HasForeignKey("PolygonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Polygon");
                });

            modelBuilder.Entity("aibase.DataEntries.DataEntry", b =>
                {
                    b.HasOne("aibase.DrillHoleEntity.DrillHole", "DrillHole")
                        .WithMany()
                        .HasForeignKey("DrillholeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.GeologySuites.GeologySuite", "GeologySuite")
                        .WithMany()
                        .HasForeignKey("GeologySuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DrillHole");

                    b.Navigation("GeologySuite");
                });

            modelBuilder.Entity("aibase.DesurveyResults.DesurveyResult", b =>
                {
                    b.HasOne("aibase.DrillHoleEntity.DrillHole", "DrillHole")
                        .WithMany("DesurveyResults")
                        .HasForeignKey("DrillHoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DrillHole");
                });

            modelBuilder.Entity("aibase.DownholeDatas.DownholeData", b =>
                {
                    b.HasOne("aibase.Suites.Suite", "Suite")
                        .WithMany()
                        .HasForeignKey("SuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Suite");
                });

            modelBuilder.Entity("aibase.DrillHoleEntity.DrillHole", b =>
                {
                    b.HasOne("aibase.ExportTemplates.ExportTemplate", null)
                        .WithMany("DrillHoles")
                        .HasForeignKey("ExportTemplateId");

                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.Prospects.Prospect", "Prospect")
                        .WithMany()
                        .HasForeignKey("ProspectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");

                    b.Navigation("Prospect");
                });

            modelBuilder.Entity("aibase.ExportEvents.ExportEvent", b =>
                {
                    b.HasOne("aibase.ExportTemplates.ExportTemplate", "ExportTemplate")
                        .WithMany()
                        .HasForeignKey("ExportTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExportTemplate");
                });

            modelBuilder.Entity("aibase.ExportTemplateDrillHoles.ExportTemplateDrillHole", b =>
                {
                    b.HasOne("aibase.DrillHoleEntity.DrillHole", "DrillHole")
                        .WithMany()
                        .HasForeignKey("DrillHoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ExportTemplates.ExportTemplate", "ExportTemplate")
                        .WithMany("ExportTemplateDrillHoles")
                        .HasForeignKey("ExportTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DrillHole");

                    b.Navigation("ExportTemplate");
                });

            modelBuilder.Entity("aibase.ExportTemplateImageSubtypes.ExportTemplateImageSubtype", b =>
                {
                    b.HasOne("aibase.ExportTemplates.ExportTemplate", "ExportTemplate")
                        .WithMany("ExportTemplateImageSubtypes")
                        .HasForeignKey("ExportTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ImageSubtypes.ImageSubtype", "ImageSubtype")
                        .WithMany()
                        .HasForeignKey("ImageSubtypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExportTemplate");

                    b.Navigation("ImageSubtype");
                });

            modelBuilder.Entity("aibase.ExportTemplateImageTypes.ExportTemplateImageType", b =>
                {
                    b.HasOne("aibase.ExportTemplates.ExportTemplate", "ExportTemplate")
                        .WithMany("ExportTemplateImageTypes")
                        .HasForeignKey("ExportTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ImageTypes.ImageType", "ImageType")
                        .WithMany()
                        .HasForeignKey("ImageTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ExportTemplate");

                    b.Navigation("ImageType");
                });

            modelBuilder.Entity("aibase.ExportTemplates.ExportTemplate", b =>
                {
                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.Prospects.Prospect", "Prospect")
                        .WithMany()
                        .HasForeignKey("ProspectId");

                    b.Navigation("Project");

                    b.Navigation("Prospect");
                });

            modelBuilder.Entity("aibase.FileEntity.File", b =>
                {
                    b.HasOne("aibase.ImageEntity.Image", "Image")
                        .WithMany("Files")
                        .HasForeignKey("ImageId");

                    b.Navigation("Image");
                });

            modelBuilder.Entity("aibase.GeologyDatas.GeologyData", b =>
                {
                    b.HasOne("aibase.RockTypes.RockType", "RockType")
                        .WithMany()
                        .HasForeignKey("RockTypeId");

                    b.Navigation("RockType");
                });

            modelBuilder.Entity("aibase.GeologyDateValues.GeologyDateValue", b =>
                {
                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeologySuiteField");
                });

            modelBuilder.Entity("aibase.GeologyDescriptionValues.GeologyDescriptionValue", b =>
                {
                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeologySuiteField");
                });

            modelBuilder.Entity("aibase.GeologyFields.GeologyField", b =>
                {
                    b.HasOne("aibase.GeologyDates.GeologyDate", "GeologyDate")
                        .WithMany()
                        .HasForeignKey("GeologyDateId");

                    b.HasOne("aibase.GeologyDescriptions.GeologyDescription", "GeologyDescription")
                        .WithMany()
                        .HasForeignKey("GeologyDescriptionId");

                    b.HasOne("aibase.Numbers.Number", "Number")
                        .WithMany()
                        .HasForeignKey("NumberId");

                    b.HasOne("aibase.PickLists.PickList", "PickList")
                        .WithMany()
                        .HasForeignKey("PickListId");

                    b.HasOne("aibase.RockGroups.RockGroup", "RockGroup")
                        .WithMany()
                        .HasForeignKey("RockGroupId");

                    b.HasOne("aibase.RockTree.RockNode", "RockNode")
                        .WithMany()
                        .HasForeignKey("RockNodeId");

                    b.HasOne("aibase.RockSelectNumbers.RockSelectNumber", "RockSelectNumber")
                        .WithMany()
                        .HasForeignKey("RockSelectNumberId");

                    b.HasOne("aibase.RockTypeNumbers.RockTypeNumber", "RockTypeNumber")
                        .WithMany()
                        .HasForeignKey("RockTypeNumberId");

                    b.Navigation("GeologyDate");

                    b.Navigation("GeologyDescription");

                    b.Navigation("Number");

                    b.Navigation("PickList");

                    b.Navigation("RockGroup");

                    b.Navigation("RockNode");

                    b.Navigation("RockSelectNumber");

                    b.Navigation("RockTypeNumber");
                });

            modelBuilder.Entity("aibase.GeologyProjectSuites.GeologyProjectSuite", b =>
                {
                    b.HasOne("aibase.GeologySuites.GeologySuite", "GeologySuite")
                        .WithMany("GeologyProjectSuites")
                        .HasForeignKey("GeologySuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany("GeologyProjectSuites")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeologySuite");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("aibase.GeologySuiteFields.GeologySuiteField", b =>
                {
                    b.HasOne("aibase.GeologyFields.GeologyField", "GeologyField")
                        .WithMany()
                        .HasForeignKey("GeologyFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.GeologySuites.GeologySuite", "GeologySuite")
                        .WithMany("GeologySuiteFields")
                        .HasForeignKey("GeologySuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeologyField");

                    b.Navigation("GeologySuite");
                });

            modelBuilder.Entity("aibase.GeotechDatas.GeotechData", b =>
                {
                    b.HasOne("aibase.GeotechSuites.GeotechSuite", "GeotechSuite")
                        .WithMany()
                        .HasForeignKey("GeotechSuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RockGroups.RockGroup", "RockGroup")
                        .WithMany()
                        .HasForeignKey("RockGroupId");

                    b.HasOne("aibase.RockTypes.RockType", "RockType")
                        .WithMany()
                        .HasForeignKey("RockTypeId");

                    b.HasOne("aibase.StructureConditions.StructureCondition", "StructureCondition")
                        .WithMany()
                        .HasForeignKey("StructureConditionId");

                    b.HasOne("aibase.Structures.Structure", "Structure")
                        .WithMany()
                        .HasForeignKey("StructureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeotechSuite");

                    b.Navigation("RockGroup");

                    b.Navigation("RockType");

                    b.Navigation("Structure");

                    b.Navigation("StructureCondition");
                });

            modelBuilder.Entity("aibase.GeotechSuiteStructures.GeotechSuiteStructure", b =>
                {
                    b.HasOne("aibase.GeotechSuites.GeotechSuite", "GeotechSuite")
                        .WithMany("GeotechSuiteStructures")
                        .HasForeignKey("GeotechSuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.Structures.Structure", "Structure")
                        .WithMany("GeotechSuiteStructures")
                        .HasForeignKey("StructureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeotechSuite");

                    b.Navigation("Structure");
                });

            modelBuilder.Entity("aibase.ImageCrops.ImageCrop", b =>
                {
                    b.HasOne("aibase.ImageEntity.Image", "Image")
                        .WithMany("CroppedImages")
                        .HasForeignKey("ImageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Image");
                });

            modelBuilder.Entity("aibase.ImageEntity.Image", b =>
                {
                    b.HasOne("aibase.DrillHoleEntity.DrillHole", "DrillHole")
                        .WithMany()
                        .HasForeignKey("DrillHoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ImageSubtypes.ImageSubtype", "ImageSubtype")
                        .WithMany()
                        .HasForeignKey("ImageSubtypeId");

                    b.HasOne("aibase.ImageTypes.ImageType", "ImageType")
                        .WithMany()
                        .HasForeignKey("ImageTypeId");

                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.Prospects.Prospect", "Prospect")
                        .WithMany()
                        .HasForeignKey("ProspectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DrillHole");

                    b.Navigation("ImageSubtype");

                    b.Navigation("ImageType");

                    b.Navigation("Project");

                    b.Navigation("Prospect");
                });

            modelBuilder.Entity("aibase.ImageSubtypes.ImageSubtype", b =>
                {
                    b.HasOne("aibase.ImageTypes.ImageType", "ImageType")
                        .WithMany("ImageSubtypes")
                        .HasForeignKey("ImageTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ImageType");
                });

            modelBuilder.Entity("aibase.ImportMappingTemplates.ImportMappingTemplateField", b =>
                {
                    b.HasOne("aibase.ImportMappingTemplates.ImportMappingTemplate", "ImportMappingTemplate")
                        .WithMany("ImportMappingTemplateFields")
                        .HasForeignKey("ImportMappingTemplateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ImportMappingTemplate");
                });

            modelBuilder.Entity("aibase.LoggingViewColumns.LoggingViewColumn", b =>
                {
                    b.HasOne("aibase.AssayAttributes.AssayAttribute", "AssayAttribute")
                        .WithMany()
                        .HasForeignKey("AssayAttributeId");

                    b.HasOne("aibase.AssaySuites.AssaySuite", "AssaySuite")
                        .WithMany()
                        .HasForeignKey("AssaySuiteId");

                    b.HasOne("aibase.Attributes.Attribute", "Attribute")
                        .WithMany()
                        .HasForeignKey("AttributeId");

                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId");

                    b.HasOne("aibase.GeologySuites.GeologySuite", "GeologySuite")
                        .WithMany()
                        .HasForeignKey("GeologySuiteId");

                    b.HasOne("aibase.ImageSubtypes.ImageSubtype", "ImageSubtype")
                        .WithMany()
                        .HasForeignKey("ImageSubtypeId");

                    b.HasOne("aibase.ImageTypes.ImageType", "ImageType")
                        .WithMany()
                        .HasForeignKey("ImageTypeId");

                    b.HasOne("aibase.LoggingViews.LoggingView", "LoggingView")
                        .WithMany()
                        .HasForeignKey("LoggingViewId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.NumberRanges.NumberRange", "NumberRange")
                        .WithMany()
                        .HasForeignKey("NumberRangeId");

                    b.HasOne("aibase.Suites.Suite", "Suite")
                        .WithMany()
                        .HasForeignKey("SuiteId");

                    b.Navigation("AssayAttribute");

                    b.Navigation("AssaySuite");

                    b.Navigation("Attribute");

                    b.Navigation("GeologySuite");

                    b.Navigation("GeologySuiteField");

                    b.Navigation("ImageSubtype");

                    b.Navigation("ImageType");

                    b.Navigation("LoggingView");

                    b.Navigation("NumberRange");

                    b.Navigation("Suite");
                });

            modelBuilder.Entity("aibase.MultiTenancy.Tenant", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", "CreatorUser")
                        .WithMany()
                        .HasForeignKey("CreatorUserId");

                    b.HasOne("aibase.Authorization.Users.User", "DeleterUser")
                        .WithMany()
                        .HasForeignKey("DeleterUserId");

                    b.HasOne("Abp.Application.Editions.Edition", "Edition")
                        .WithMany()
                        .HasForeignKey("EditionId");

                    b.HasOne("aibase.Authorization.Users.User", "LastModifierUser")
                        .WithMany()
                        .HasForeignKey("LastModifierUserId");

                    b.Navigation("CreatorUser");

                    b.Navigation("DeleterUser");

                    b.Navigation("Edition");

                    b.Navigation("LastModifierUser");
                });

            modelBuilder.Entity("aibase.NumberRanges.NumberRangeInterval", b =>
                {
                    b.HasOne("aibase.NumberRanges.NumberRange", "NumberRange")
                        .WithMany("Intervals")
                        .HasForeignKey("NumberRangeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NumberRange");
                });

            modelBuilder.Entity("aibase.NumberValues.NumberValue", b =>
                {
                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeologySuiteField");
                });

            modelBuilder.Entity("aibase.Numbers.Number", b =>
                {
                    b.HasOne("aibase.Units.Unit", "Unit")
                        .WithMany()
                        .HasForeignKey("UnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("aibase.PickListItems.PickListItem", b =>
                {
                    b.HasOne("aibase.PickLists.PickList", "PickList")
                        .WithMany("PickListItems")
                        .HasForeignKey("PickListId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PickList");
                });

            modelBuilder.Entity("aibase.PickListValues.PickListValue", b =>
                {
                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.PickListItems.PickListItem", "PickListItem")
                        .WithMany()
                        .HasForeignKey("PickListItemId");

                    b.Navigation("GeologySuiteField");

                    b.Navigation("PickListItem");
                });

            modelBuilder.Entity("aibase.ProjectEntity.Project", b =>
                {
                    b.HasOne("aibase.MobileProfiles.MobileProfile", "MobileProfile")
                        .WithMany("Projects")
                        .HasForeignKey("MobileProfileId");

                    b.HasOne("aibase.RockGroups.RockGroup", "RockGroup")
                        .WithMany()
                        .HasForeignKey("RockGroupId");

                    b.HasOne("aibase.WorkflowEntity.Workflow", "Workflow")
                        .WithMany()
                        .HasForeignKey("WorkflowId");

                    b.Navigation("MobileProfile");

                    b.Navigation("RockGroup");

                    b.Navigation("Workflow");
                });

            modelBuilder.Entity("aibase.ProjectGeotechSuites.ProjectGeotechSuite", b =>
                {
                    b.HasOne("aibase.GeotechSuites.GeotechSuite", "GeotechSuite")
                        .WithMany("ProjectGeotechSuites")
                        .HasForeignKey("GeotechSuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany("ProjectGeotechSuites")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeotechSuite");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("aibase.ProjectImageTypes.ProjectImageType", b =>
                {
                    b.HasOne("aibase.ImageTypes.ImageType", "ImageType")
                        .WithMany("ProjectImageTypes")
                        .HasForeignKey("ImageTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany("ProjectImageTypes")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ImageType");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("aibase.ProjectLoggingViews.ProjectLoggingView", b =>
                {
                    b.HasOne("aibase.LoggingViews.LoggingView", "LoggingView")
                        .WithMany("ProjectLoggingViews")
                        .HasForeignKey("LoggingViewId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany("ProjectLoggingViews")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LoggingView");

                    b.Navigation("Project");
                });

            modelBuilder.Entity("aibase.ProjectSuites.ProjectSuite", b =>
                {
                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany("ProjectSuites")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.Suites.Suite", "Suite")
                        .WithMany("SuiteProjects")
                        .HasForeignKey("SuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");

                    b.Navigation("Suite");
                });

            modelBuilder.Entity("aibase.Prospects.Prospect", b =>
                {
                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");
                });

            modelBuilder.Entity("aibase.RecoveryResults.RecoveryResult", b =>
                {
                    b.HasOne("aibase.DrillHoleEntity.DrillHole", "DrillHole")
                        .WithMany()
                        .HasForeignKey("DrillHoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DrillHole");
                });

            modelBuilder.Entity("aibase.RockGroupProjects.RockGroupProject", b =>
                {
                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RockGroups.RockGroup", "RockGroup")
                        .WithMany("RockGroupProjects")
                        .HasForeignKey("RockGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");

                    b.Navigation("RockGroup");
                });

            modelBuilder.Entity("aibase.RockGroupRockTypes.RockGroupRockType", b =>
                {
                    b.HasOne("aibase.RockGroups.RockGroup", "RockGroup")
                        .WithMany("RockGroupRockTypes")
                        .HasForeignKey("RockGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RockTypes.RockType", "RockType")
                        .WithMany("RockGroupRockTypes")
                        .HasForeignKey("RockTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RockGroup");

                    b.Navigation("RockType");
                });

            modelBuilder.Entity("aibase.RockGroupValues.RockGroupValue", b =>
                {
                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RockTypes.RockType", "RockType")
                        .WithMany()
                        .HasForeignKey("RockTypeId");

                    b.Navigation("GeologySuiteField");

                    b.Navigation("RockType");
                });

            modelBuilder.Entity("aibase.RockLines.RockLine", b =>
                {
                    b.HasOne("aibase.ImageCrops.ImageCrop", "ImageCrop")
                        .WithMany("RockLines")
                        .HasForeignKey("ImageCropId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ImageCrop");
                });

            modelBuilder.Entity("aibase.RockNodeValues.RockNodeValue", b =>
                {
                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RockTree.RockNode", "RockNode")
                        .WithMany()
                        .HasForeignKey("RockNodeId");

                    b.Navigation("GeologySuiteField");

                    b.Navigation("RockNode");
                });

            modelBuilder.Entity("aibase.RockSelectNumberValues.RockSelectNumberValue", b =>
                {
                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RockTypes.RockType", "RockType")
                        .WithMany()
                        .HasForeignKey("RockTypeId");

                    b.Navigation("GeologySuiteField");

                    b.Navigation("RockType");
                });

            modelBuilder.Entity("aibase.RockSelectNumbers.RockSelectNumber", b =>
                {
                    b.HasOne("aibase.Numbers.Number", "Number")
                        .WithMany()
                        .HasForeignKey("NumberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RockGroups.RockGroup", "RockGroup")
                        .WithMany()
                        .HasForeignKey("RockGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Number");

                    b.Navigation("RockGroup");
                });

            modelBuilder.Entity("aibase.RockTree.RockNode", b =>
                {
                    b.HasOne("aibase.RockTypes.RockType", "RockType")
                        .WithMany()
                        .HasForeignKey("RockTypeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("RockType");
                });

            modelBuilder.Entity("aibase.RockTree.RockNodeRelation", b =>
                {
                    b.HasOne("aibase.RockTree.RockNode", "ChildNode")
                        .WithMany("ParentRelations")
                        .HasForeignKey("ChildNodeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("aibase.RockTree.RockNode", "ParentNode")
                        .WithMany("ChildRelations")
                        .HasForeignKey("ParentNodeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ChildNode");

                    b.Navigation("ParentNode");
                });

            modelBuilder.Entity("aibase.RockTree.RockTreeRoot", b =>
                {
                    b.HasOne("aibase.GeologySuites.GeologySuite", null)
                        .WithOne("RockTreeRoot")
                        .HasForeignKey("aibase.RockTree.RockTreeRoot", "GeologySuiteId");
                });

            modelBuilder.Entity("aibase.RockTree.RockTreeRootNode", b =>
                {
                    b.HasOne("aibase.RockTree.RockNode", "RockNode")
                        .WithMany("RockTreeRootNodes")
                        .HasForeignKey("RockNodeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RockTree.RockTreeRoot", "RockTreeRoot")
                        .WithMany("RockTreeRootNodes")
                        .HasForeignKey("RockTreeRootId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RockNode");

                    b.Navigation("RockTreeRoot");
                });

            modelBuilder.Entity("aibase.RockTypeNumberValues.RockTypeNumberValue", b =>
                {
                    b.HasOne("aibase.GeologySuiteFields.GeologySuiteField", "GeologySuiteField")
                        .WithMany()
                        .HasForeignKey("GeologySuiteFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeologySuiteField");
                });

            modelBuilder.Entity("aibase.RockTypeNumbers.RockTypeNumber", b =>
                {
                    b.HasOne("aibase.Numbers.Number", "Number")
                        .WithMany()
                        .HasForeignKey("NumberId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RockTypes.RockType", "RockType")
                        .WithMany()
                        .HasForeignKey("RockTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Number");

                    b.Navigation("RockType");
                });

            modelBuilder.Entity("aibase.RockTypes.RockType", b =>
                {
                    b.HasOne("aibase.RockStyles.RockStyle", "RockStyle")
                        .WithMany()
                        .HasForeignKey("RockStyleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RockStyle");
                });

            modelBuilder.Entity("aibase.RqdCalculations.RqdCalculation", b =>
                {
                    b.HasOne("aibase.Structures.Structure", "Structure")
                        .WithMany()
                        .HasForeignKey("StructureId");

                    b.HasOne("aibase.StructureTypes.StructureType", "StructureType")
                        .WithMany()
                        .HasForeignKey("StructureTypeId");

                    b.Navigation("Structure");

                    b.Navigation("StructureType");
                });

            modelBuilder.Entity("aibase.RqdCalculations.RqdCalculationProject", b =>
                {
                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany("RqdCalculationProjects")
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RqdCalculations.RqdCalculation", "RqdCalculation")
                        .WithMany("RqdCalculationProjects")
                        .HasForeignKey("RqdCalculationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");

                    b.Navigation("RqdCalculation");
                });

            modelBuilder.Entity("aibase.RqdCalculations.RqdCalculationResult", b =>
                {
                    b.HasOne("aibase.DrillHoleEntity.DrillHole", "DrillHole")
                        .WithMany()
                        .HasForeignKey("DrillHoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RqdCalculations.RqdCalculation", "RqdCalculation")
                        .WithMany()
                        .HasForeignKey("RqdCalculationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DrillHole");

                    b.Navigation("RqdCalculation");
                });

            modelBuilder.Entity("aibase.RqdPercentResults.RqdPercentResult", b =>
                {
                    b.HasOne("aibase.DrillHoleEntity.DrillHole", "DrillHole")
                        .WithMany()
                        .HasForeignKey("DrillHoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.RqdCalculations.RqdCalculation", "RqdCalculation")
                        .WithMany()
                        .HasForeignKey("RqdCalculationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DrillHole");

                    b.Navigation("RqdCalculation");
                });

            modelBuilder.Entity("aibase.StepWorkflowEntity.StepWorkflow", b =>
                {
                    b.HasOne("aibase.Polygons.Polygon", "BoundingBox")
                        .WithMany()
                        .HasForeignKey("BoundingBoxId");

                    b.HasOne("aibase.Polygons.Polygon", "BoundingRows")
                        .WithMany()
                        .HasForeignKey("BoundingRowsId");

                    b.Navigation("BoundingBox");

                    b.Navigation("BoundingRows");
                });

            modelBuilder.Entity("aibase.Structures.Structure", b =>
                {
                    b.HasOne("aibase.RockGroups.RockGroup", "RockGroup")
                        .WithMany()
                        .HasForeignKey("RockGroupId");

                    b.HasOne("aibase.StructureTypes.StructureType", "StructureType")
                        .WithMany()
                        .HasForeignKey("StructureTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RockGroup");

                    b.Navigation("StructureType");
                });

            modelBuilder.Entity("aibase.SuiteAttributes.SuiteAttribute", b =>
                {
                    b.HasOne("aibase.Attributes.Attribute", "Attribute")
                        .WithMany("SuiteAttributes")
                        .HasForeignKey("AttributeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.Suites.Suite", "Suite")
                        .WithMany("SuiteAttributes")
                        .HasForeignKey("SuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Attribute");

                    b.Navigation("Suite");
                });

            modelBuilder.Entity("aibase.TrayDepthResults.TrayDepthResult", b =>
                {
                    b.HasOne("aibase.DrillHoleEntity.DrillHole", "DrillHole")
                        .WithMany()
                        .HasForeignKey("DrillHoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DrillHole");
                });

            modelBuilder.Entity("aibase.UserProjects.UserProject", b =>
                {
                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.Authorization.Users.User", "User")
                        .WithMany("UserProjects")
                        .HasForeignKey("userId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Project");

                    b.Navigation("User");
                });

            modelBuilder.Entity("aibase.WorkRoleGeotechSuites.WorkRoleGeotechSuite", b =>
                {
                    b.HasOne("aibase.GeotechSuites.GeotechSuite", "GeotechSuite")
                        .WithMany("WorkRoleGeotechSuites")
                        .HasForeignKey("GeotechSuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.WorkRoles.WorkRole", "WorkRole")
                        .WithMany("WorkRoleGeotechSuites")
                        .HasForeignKey("WorkRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeotechSuite");

                    b.Navigation("WorkRole");
                });

            modelBuilder.Entity("aibase.WorkRoleSuites.WorkRoleSuite", b =>
                {
                    b.HasOne("aibase.GeologySuites.GeologySuite", "GeologySuite")
                        .WithMany()
                        .HasForeignKey("GeologySuiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.WorkRoles.WorkRole", "WorkRole")
                        .WithMany("WorkRoleSuites")
                        .HasForeignKey("WorkRoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeologySuite");

                    b.Navigation("WorkRole");
                });

            modelBuilder.Entity("aibase.WorkflowJobs.WorkflowJob", b =>
                {
                    b.HasOne("aibase.DrillHoleEntity.DrillHole", "DrillHole")
                        .WithMany()
                        .HasForeignKey("DrillholeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.ImageSubtypes.ImageSubtype", "ImageSubtype")
                        .WithMany()
                        .HasForeignKey("ImageSubtypeId");

                    b.HasOne("aibase.ImageTypes.ImageType", "ImageType")
                        .WithMany()
                        .HasForeignKey("ImageTypeId");

                    b.HasOne("aibase.ProjectEntity.Project", "Project")
                        .WithMany()
                        .HasForeignKey("ProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.Prospects.Prospect", "Prospect")
                        .WithMany()
                        .HasForeignKey("ProspectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.WorkflowEntity.Workflow", "Workflow")
                        .WithMany()
                        .HasForeignKey("WorkflowId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DrillHole");

                    b.Navigation("ImageSubtype");

                    b.Navigation("ImageType");

                    b.Navigation("Project");

                    b.Navigation("Prospect");

                    b.Navigation("Workflow");
                });

            modelBuilder.Entity("aibase.WorkflowJobs.WorkflowJobError", b =>
                {
                    b.HasOne("aibase.ImageEntity.Image", "Image")
                        .WithMany()
                        .HasForeignKey("ImageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("aibase.WorkflowJobs.WorkflowJob", "WorkflowJob")
                        .WithMany()
                        .HasForeignKey("WorkflowJobId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Image");

                    b.Navigation("WorkflowJob");
                });

            modelBuilder.Entity("Abp.Application.Features.EditionFeatureSetting", b =>
                {
                    b.HasOne("Abp.Application.Editions.Edition", "Edition")
                        .WithMany()
                        .HasForeignKey("EditionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Edition");
                });

            modelBuilder.Entity("Abp.Authorization.Roles.RolePermissionSetting", b =>
                {
                    b.HasOne("aibase.Authorization.Roles.Role", null)
                        .WithMany("Permissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Abp.Authorization.Users.UserPermissionSetting", b =>
                {
                    b.HasOne("aibase.Authorization.Users.User", null)
                        .WithMany("Permissions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Abp.DynamicEntityProperties.DynamicProperty", b =>
                {
                    b.Navigation("DynamicPropertyValues");
                });

            modelBuilder.Entity("Abp.EntityHistory.EntityChange", b =>
                {
                    b.Navigation("PropertyChanges");
                });

            modelBuilder.Entity("Abp.EntityHistory.EntityChangeSet", b =>
                {
                    b.Navigation("EntityChanges");
                });

            modelBuilder.Entity("Abp.Organizations.OrganizationUnit", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("aibase.APIKeys.APIKey", b =>
                {
                    b.Navigation("APIKeyRoles");
                });

            modelBuilder.Entity("aibase.APIKeys.APIKeyTypes", b =>
                {
                    b.Navigation("APIKeyRoles");
                });

            modelBuilder.Entity("aibase.AssayAttributes.AssayAttribute", b =>
                {
                    b.Navigation("AssaySuiteAttributes");
                });

            modelBuilder.Entity("aibase.AssaySuites.AssaySuite", b =>
                {
                    b.Navigation("AssayProjectSuites");

                    b.Navigation("AssaySuiteAttributes");

                    b.Navigation("AssaySuiteFields");
                });

            modelBuilder.Entity("aibase.Attributes.Attribute", b =>
                {
                    b.Navigation("SuiteAttributes");
                });

            modelBuilder.Entity("aibase.Authorization.Roles.Role", b =>
                {
                    b.Navigation("Claims");

                    b.Navigation("Permissions");
                });

            modelBuilder.Entity("aibase.Authorization.Users.User", b =>
                {
                    b.Navigation("Claims");

                    b.Navigation("Logins");

                    b.Navigation("Permissions");

                    b.Navigation("Roles");

                    b.Navigation("Settings");

                    b.Navigation("Tokens");

                    b.Navigation("UserProjects");
                });

            modelBuilder.Entity("aibase.DrillHoleEntity.DrillHole", b =>
                {
                    b.Navigation("DesurveyResults");
                });

            modelBuilder.Entity("aibase.ExportTemplates.ExportTemplate", b =>
                {
                    b.Navigation("DrillHoles");

                    b.Navigation("ExportTemplateDrillHoles");

                    b.Navigation("ExportTemplateImageSubtypes");

                    b.Navigation("ExportTemplateImageTypes");
                });

            modelBuilder.Entity("aibase.GeologySuites.GeologySuite", b =>
                {
                    b.Navigation("GeologyProjectSuites");

                    b.Navigation("GeologySuiteFields");

                    b.Navigation("RockTreeRoot");
                });

            modelBuilder.Entity("aibase.GeotechSuites.GeotechSuite", b =>
                {
                    b.Navigation("GeotechSuiteStructures");

                    b.Navigation("ProjectGeotechSuites");

                    b.Navigation("WorkRoleGeotechSuites");
                });

            modelBuilder.Entity("aibase.ImageCrops.ImageCrop", b =>
                {
                    b.Navigation("RockLines");
                });

            modelBuilder.Entity("aibase.ImageEntity.Image", b =>
                {
                    b.Navigation("CroppedImages");

                    b.Navigation("Files");
                });

            modelBuilder.Entity("aibase.ImageTypes.ImageType", b =>
                {
                    b.Navigation("ImageSubtypes");

                    b.Navigation("ProjectImageTypes");
                });

            modelBuilder.Entity("aibase.ImportMappingTemplates.ImportMappingTemplate", b =>
                {
                    b.Navigation("ImportMappingTemplateFields");
                });

            modelBuilder.Entity("aibase.LoggingViews.LoggingView", b =>
                {
                    b.Navigation("ProjectLoggingViews");
                });

            modelBuilder.Entity("aibase.MobileProfiles.MobileProfile", b =>
                {
                    b.Navigation("Projects");
                });

            modelBuilder.Entity("aibase.NumberRanges.NumberRange", b =>
                {
                    b.Navigation("Intervals");
                });

            modelBuilder.Entity("aibase.PickLists.PickList", b =>
                {
                    b.Navigation("PickListItems");
                });

            modelBuilder.Entity("aibase.Polygons.Polygon", b =>
                {
                    b.Navigation("CoordinatePolygons");
                });

            modelBuilder.Entity("aibase.ProjectEntity.Project", b =>
                {
                    b.Navigation("AssayProjectSuites");

                    b.Navigation("GeologyProjectSuites");

                    b.Navigation("ProjectGeotechSuites");

                    b.Navigation("ProjectImageTypes");

                    b.Navigation("ProjectLoggingViews");

                    b.Navigation("ProjectSuites");

                    b.Navigation("RqdCalculationProjects");
                });

            modelBuilder.Entity("aibase.RockGroups.RockGroup", b =>
                {
                    b.Navigation("RockGroupProjects");

                    b.Navigation("RockGroupRockTypes");
                });

            modelBuilder.Entity("aibase.RockTree.RockNode", b =>
                {
                    b.Navigation("ChildRelations");

                    b.Navigation("ParentRelations");

                    b.Navigation("RockTreeRootNodes");
                });

            modelBuilder.Entity("aibase.RockTree.RockTreeRoot", b =>
                {
                    b.Navigation("RockTreeRootNodes");
                });

            modelBuilder.Entity("aibase.RockTypes.RockType", b =>
                {
                    b.Navigation("RockGroupRockTypes");
                });

            modelBuilder.Entity("aibase.RqdCalculations.RqdCalculation", b =>
                {
                    b.Navigation("RqdCalculationProjects");
                });

            modelBuilder.Entity("aibase.Structures.Structure", b =>
                {
                    b.Navigation("GeotechSuiteStructures");
                });

            modelBuilder.Entity("aibase.Suites.Suite", b =>
                {
                    b.Navigation("SuiteAttributes");

                    b.Navigation("SuiteProjects");
                });

            modelBuilder.Entity("aibase.WorkRoles.WorkRole", b =>
                {
                    b.Navigation("WorkRoleGeotechSuites");

                    b.Navigation("WorkRoleSuites");
                });
#pragma warning restore 612, 618
        }
    }
}
